"""
SaaS-specific KPI calculations for financial analysis.
"""

from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, timedelta
import pandas as pd
import logging

logger = logging.getLogger(__name__)


class SaaSKPICalculator:
    """Calculate SaaS-specific financial KPIs."""

    def calculate_comprehensive_kpis(
        self, organization_id: int, period_start: datetime, period_end: datetime
    ) -> Dict[str, Any]:
        """Calculate comprehensive SaaS KPIs."""

        try:
            # Get revenue data
            revenue_data = self._get_revenue_data(
                organization_id, period_start, period_end
            )

            # Calculate core SaaS metrics
            kpis = {
                "revenue_metrics": self._calculate_revenue_metrics(revenue_data),
                "customer_metrics": self._calculate_customer_metrics(
                    organization_id, period_start, period_end
                ),
                "unit_economics": self._calculate_unit_economics(
                    organization_id, period_start, period_end
                ),
                "growth_metrics": self._calculate_growth_metrics(
                    organization_id, period_start, period_end
                ),
                "efficiency_metrics": self._calculate_efficiency_metrics(
                    organization_id, period_start, period_end
                ),
            }

            # Add benchmarking
            kpis["benchmarks"] = self._get_industry_benchmarks()

            return {
                "organization_id": organization_id,
                "period": {
                    "start": period_start.isoformat(),
                    "end": period_end.isoformat(),
                },
                "kpis": kpis,
                "calculated_at": datetime.utcnow().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error calculating SaaS KPIs: {e}")
            raise

    def _calculate_revenue_metrics(self, revenue_data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate revenue-specific metrics."""
        return {
            "monthly_recurring_revenue": self._calculate_mrr(revenue_data),
            "annual_recurring_revenue": self._calculate_arr(revenue_data),
            "revenue_growth_rate": self._calculate_revenue_growth(revenue_data),
            "revenue_churn_rate": self._calculate_revenue_churn(revenue_data),
        }
