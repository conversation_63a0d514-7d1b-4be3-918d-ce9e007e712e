"""
Discounted Cash Flow (DCF) valuation model with sensitivity analysis.
"""

from typing import Dict, Any, List, Optional
from decimal import Decimal
import numpy as np
import pandas as pd
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class DCFValuation:
    """DCF valuation model with scenario analysis."""

    def __init__(self):
        self.risk_free_rate = 0.045  # Current 10-year Treasury
        self.market_risk_premium = 0.065  # Historical equity risk premium

    def calculate_dcf_valuation(
        self,
        financial_projections: List[Dict[str, Any]],
        discount_rate: float,
        terminal_growth_rate: float,
        scenarios: Optional[List[str]] = None,
    ) -> Dict[str, Any]:
        """Calculate DCF valuation with scenario analysis."""

        try:
            scenarios = scenarios or ["base"]
            results = {}

            for scenario in scenarios:
                scenario_projections = self._adjust_projections_for_scenario(
                    financial_projections, scenario
                )

                # Calculate present value of cash flows
                pv_cash_flows = self._calculate_present_value_cash_flows(
                    scenario_projections, discount_rate
                )

                # Calculate terminal value
                terminal_value = self._calculate_terminal_value(
                    scenario_projections[-1]["free_cash_flow"],
                    discount_rate,
                    terminal_growth_rate,
                )

                # Calculate enterprise value
                enterprise_value = sum(pv_cash_flows) + terminal_value

                results[scenario] = {
                    "enterprise_value": enterprise_value,
                    "terminal_value": terminal_value,
                    "pv_cash_flows": pv_cash_flows,
                    "discount_rate": discount_rate,
                    "terminal_growth_rate": terminal_growth_rate,
                    "projections": scenario_projections,
                }

            # Add sensitivity analysis
            sensitivity_analysis = self._perform_sensitivity_analysis(
                financial_projections, discount_rate, terminal_growth_rate
            )

            return {
                "valuation_results": results,
                "sensitivity_analysis": sensitivity_analysis,
                "valuation_date": datetime.utcnow().isoformat(),
                "methodology": "Discounted Cash Flow (DCF)",
                "assumptions": {
                    "base_discount_rate": discount_rate,
                    "terminal_growth_rate": terminal_growth_rate,
                    "projection_years": len(financial_projections),
                },
            }

        except Exception as e:
            logger.error(f"Error in DCF calculation: {e}")
            raise
