"""
NASDAQ-compliant Cash Flow Statement generator with indirect and direct methods.
"""

from typing import Dict, Any
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


class CashFlowGenerator:
    """Generate NASDAQ-compliant cash flow statements."""

    def __init__(self, db_session):
        self.db = db_session

    def generate_cash_flow_statement(
        self,
        organization_id: int,
        from_date: datetime,
        to_date: datetime,
        method: str = "indirect",
        include_comparative: bool = False,
    ) -> Dict[str, Any]:
        """Generate cash flow statement using indirect or direct method."""

        try:
            # Get organization and validate
            organization = self._get_organization(organization_id)

            # Generate cash flow data
            if method == "indirect":
                cash_flow_data = self._generate_indirect_cash_flow(
                    organization_id, from_date, to_date, include_comparative
                )
            else:
                cash_flow_data = self._generate_direct_cash_flow(
                    organization_id, from_date, to_date, include_comparative
                )

            # Add header information
            cash_flow_data["header"] = {
                "company_name": organization.name,
                "statement_title": "CONSOLIDATED STATEMENTS OF CASH FLOWS",
                "period_description": self._format_period(from_date, to_date),
                "comparative_period": (
                    self._format_comparative_period(from_date, to_date)
                    if include_comparative
                    else None
                ),
                "method": method.title(),
                "amounts_in": "thousands",
                "currency": "USD",
                "reporting_date": to_date.strftime("%Y-%m-%d"),
                "prepared_date": datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC"),
            }

            # Add financial analysis
            cash_flow_data["financial_analysis"] = self._calculate_cash_flow_analysis(
                cash_flow_data
            )

            return cash_flow_data

        except Exception as e:
            logger.error(f"Error generating cash flow statement: {e}")
            raise
