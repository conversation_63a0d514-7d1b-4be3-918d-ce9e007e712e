"""
NASDAQ-compliant Cash Flow Statement generator with indirect and direct methods.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
import logging
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from ...db.models import Organization, Transaction, Account
from ..financial_calculators import FinancialCalculator

logger = logging.getLogger(__name__)


class CashFlowGenerator:
    """Generate NASDAQ-compliant cash flow statements."""

    def __init__(self, db_session: Session):
        self.db = db_session
        self.precision = Decimal("0.01")
        self.financial_calc = FinancialCalculator()

    def _round_currency(self, value) -> Decimal:
        """Round currency values to 2 decimal places."""
        if isinstance(value, Decimal):
            return value.quantize(self.precision, rounding=ROUND_HALF_UP)
        return Decimal(str(value)).quantize(self.precision, rounding=ROUND_HALF_UP)

    def generate_cash_flow_statement(
        self,
        organization_id: int,
        from_date: datetime,
        to_date: datetime,
        method: str = "indirect",
        include_comparative: bool = False,
    ) -> Dict[str, Any]:
        """Generate cash flow statement using indirect or direct method."""

        try:
            # Get organization and validate
            organization = self._get_organization(organization_id)

            # Generate cash flow data
            if method == "indirect":
                cash_flow_data = self._generate_indirect_cash_flow(
                    organization_id, from_date, to_date, include_comparative
                )
            else:
                cash_flow_data = self._generate_direct_cash_flow(
                    organization_id, from_date, to_date, include_comparative
                )

            # Add header information
            cash_flow_data["header"] = {
                "company_name": organization.name,
                "statement_title": "CONSOLIDATED STATEMENTS OF CASH FLOWS",
                "period_description": self._format_period(from_date, to_date),
                "comparative_period": (
                    self._format_comparative_period(from_date, to_date)
                    if include_comparative
                    else None
                ),
                "method": method.title(),
                "amounts_in": "thousands",
                "currency": "USD",
                "reporting_date": to_date.strftime("%Y-%m-%d"),
                "prepared_date": datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S UTC"),
            }

            # Add financial analysis
            cash_flow_data["financial_analysis"] = self._calculate_cash_flow_analysis(
                cash_flow_data
            )

            return cash_flow_data

        except Exception as e:
            logger.error(f"Error generating cash flow statement: {e}")
            raise

    def _get_organization(self, organization_id: int) -> Organization:
        """Get organization by ID."""
        organization = self.db.query(Organization).filter(
            Organization.id == organization_id
        ).first()

        if not organization:
            raise ValueError(f"Organization with ID {organization_id} not found")

        return organization

    def _format_period(self, from_date: datetime, to_date: datetime) -> str:
        """Format period description for financial statement."""
        if from_date.year == to_date.year:
            if from_date.month == 1 and to_date.month == 12:
                return f"Year Ended December 31, {to_date.year}"
            else:
                return f"Period from {from_date.strftime('%B %d, %Y')} to {to_date.strftime('%B %d, %Y')}"
        else:
            return f"Period from {from_date.strftime('%B %d, %Y')} to {to_date.strftime('%B %d, %Y')}"

    def _format_comparative_period(self, from_date: datetime, to_date: datetime) -> str:
        """Format comparative period description."""
        comp_from = from_date - timedelta(days=365)
        comp_to = to_date - timedelta(days=365)
        return self._format_period(comp_from, comp_to)

    def _generate_indirect_cash_flow(
        self,
        organization_id: int,
        from_date: datetime,
        to_date: datetime,
        include_comparative: bool = False,
    ) -> Dict[str, Any]:
        """Generate cash flow statement using indirect method (starts with net income)."""

        try:
            # Get net income from income statement
            net_income = self._get_net_income(organization_id, from_date, to_date)

            # Calculate operating activities (indirect method)
            operating_activities = self._calculate_operating_activities_indirect(
                organization_id, from_date, to_date, net_income
            )

            # Calculate investing activities
            investing_activities = self._calculate_investing_activities(
                organization_id, from_date, to_date
            )

            # Calculate financing activities
            financing_activities = self._calculate_financing_activities(
                organization_id, from_date, to_date
            )

            # Calculate net change in cash
            net_change_in_cash = (
                operating_activities["net_cash_from_operating"] +
                investing_activities["net_cash_from_investing"] +
                financing_activities["net_cash_from_financing"]
            )

            # Get beginning and ending cash balances
            beginning_cash = self._get_cash_balance(organization_id, from_date, beginning=True)
            ending_cash = beginning_cash + net_change_in_cash

            cash_flow_data = {
                "method": "indirect",
                "period": {
                    "from_date": from_date.isoformat(),
                    "to_date": to_date.isoformat(),
                },
                "operating_activities": operating_activities,
                "investing_activities": investing_activities,
                "financing_activities": financing_activities,
                "cash_summary": {
                    "net_change_in_cash": float(self._round_currency(net_change_in_cash)),
                    "beginning_cash": float(self._round_currency(beginning_cash)),
                    "ending_cash": float(self._round_currency(ending_cash)),
                },
            }

            # Add comparative period if requested
            if include_comparative:
                comp_from = from_date - timedelta(days=365)
                comp_to = to_date - timedelta(days=365)
                cash_flow_data["comparative_period"] = self._generate_indirect_cash_flow(
                    organization_id, comp_from, comp_to, include_comparative=False
                )

            return cash_flow_data

        except Exception as e:
            logger.error(f"Error generating indirect cash flow: {e}")
            raise

    def _generate_direct_cash_flow(
        self,
        organization_id: int,
        from_date: datetime,
        to_date: datetime,
        include_comparative: bool = False,
    ) -> Dict[str, Any]:
        """Generate cash flow statement using direct method (shows actual cash receipts/payments)."""

        try:
            # Calculate operating activities (direct method)
            operating_activities = self._calculate_operating_activities_direct(
                organization_id, from_date, to_date
            )

            # Calculate investing activities (same as indirect)
            investing_activities = self._calculate_investing_activities(
                organization_id, from_date, to_date
            )

            # Calculate financing activities (same as indirect)
            financing_activities = self._calculate_financing_activities(
                organization_id, from_date, to_date
            )

            # Calculate net change in cash
            net_change_in_cash = (
                operating_activities["net_cash_from_operating"] +
                investing_activities["net_cash_from_investing"] +
                financing_activities["net_cash_from_financing"]
            )

            # Get beginning and ending cash balances
            beginning_cash = self._get_cash_balance(organization_id, from_date, beginning=True)
            ending_cash = beginning_cash + net_change_in_cash

            cash_flow_data = {
                "method": "direct",
                "period": {
                    "from_date": from_date.isoformat(),
                    "to_date": to_date.isoformat(),
                },
                "operating_activities": operating_activities,
                "investing_activities": investing_activities,
                "financing_activities": financing_activities,
                "cash_summary": {
                    "net_change_in_cash": float(self._round_currency(net_change_in_cash)),
                    "beginning_cash": float(self._round_currency(beginning_cash)),
                    "ending_cash": float(self._round_currency(ending_cash)),
                },
            }

            # Add comparative period if requested
            if include_comparative:
                comp_from = from_date - timedelta(days=365)
                comp_to = to_date - timedelta(days=365)
                cash_flow_data["comparative_period"] = self._generate_direct_cash_flow(
                    organization_id, comp_from, comp_to, include_comparative=False
                )

            return cash_flow_data

        except Exception as e:
            logger.error(f"Error generating direct cash flow: {e}")
            raise

    def _get_net_income(self, organization_id: int, from_date: datetime, to_date: datetime) -> Decimal:
        """Calculate net income for the period."""
        try:
            # Get revenue transactions
            revenue_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    Account.type.in_(["REVENUE", "SALES", "INCOME"])
                )
            )
            total_revenue = revenue_query.scalar() or 0

            # Get expense transactions
            expense_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    Account.type.in_(["EXPENSE", "COST", "COGS"])
                )
            )
            total_expenses = expense_query.scalar() or 0

            net_income = self._round_currency(total_revenue - total_expenses)
            return net_income

        except Exception as e:
            logger.error(f"Error calculating net income: {e}")
            raise

    def _calculate_operating_activities_indirect(
        self,
        organization_id: int,
        from_date: datetime,
        to_date: datetime,
        net_income: Decimal,
    ) -> Dict[str, Any]:
        """Calculate operating activities using indirect method."""
        try:
            # Start with net income
            adjustments = {}

            # Add back non-cash expenses
            depreciation = self._get_depreciation_expense(organization_id, from_date, to_date)
            adjustments["depreciation_amortization"] = float(depreciation)

            # Changes in working capital
            working_capital_changes = self._calculate_working_capital_changes(
                organization_id, from_date, to_date
            )

            # Calculate net cash from operating activities
            net_cash_operating = (
                net_income +
                depreciation +
                Decimal(str(working_capital_changes["accounts_receivable_change"])) +
                Decimal(str(working_capital_changes["inventory_change"])) +
                Decimal(str(working_capital_changes["accounts_payable_change"])) +
                Decimal(str(working_capital_changes["accrued_expenses_change"]))
            )

            return {
                "net_income": float(net_income),
                "adjustments": adjustments,
                "working_capital_changes": working_capital_changes,
                "net_cash_from_operating": float(self._round_currency(net_cash_operating)),
            }

        except Exception as e:
            logger.error(f"Error calculating indirect operating activities: {e}")
            raise

    def _calculate_operating_activities_direct(
        self,
        organization_id: int,
        from_date: datetime,
        to_date: datetime,
    ) -> Dict[str, Any]:
        """Calculate operating activities using direct method."""
        try:
            # Cash receipts from customers
            cash_receipts = self._get_cash_receipts_from_customers(
                organization_id, from_date, to_date
            )

            # Cash payments to suppliers and employees
            cash_payments_suppliers = self._get_cash_payments_to_suppliers(
                organization_id, from_date, to_date
            )
            cash_payments_employees = self._get_cash_payments_to_employees(
                organization_id, from_date, to_date
            )

            # Other operating cash flows
            interest_paid = self._get_interest_paid(organization_id, from_date, to_date)
            taxes_paid = self._get_taxes_paid(organization_id, from_date, to_date)

            # Calculate net cash from operating activities
            net_cash_operating = (
                cash_receipts -
                cash_payments_suppliers -
                cash_payments_employees -
                interest_paid -
                taxes_paid
            )

            return {
                "cash_receipts_from_customers": float(self._round_currency(cash_receipts)),
                "cash_payments_to_suppliers": float(self._round_currency(-cash_payments_suppliers)),
                "cash_payments_to_employees": float(self._round_currency(-cash_payments_employees)),
                "interest_paid": float(self._round_currency(-interest_paid)),
                "taxes_paid": float(self._round_currency(-taxes_paid)),
                "net_cash_from_operating": float(self._round_currency(net_cash_operating)),
            }

        except Exception as e:
            logger.error(f"Error calculating direct operating activities: {e}")
            raise

    def _get_depreciation_expense(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get depreciation and amortization expense for the period."""
        try:
            depreciation_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%depreciation%"),
                        Account.name.ilike("%amortization%"),
                        Account.type == "DEPRECIATION"
                    )
                )
            )
            depreciation = depreciation_query.scalar() or 0
            return self._round_currency(abs(depreciation))  # Ensure positive value

        except Exception as e:
            logger.error(f"Error getting depreciation expense: {e}")
            return Decimal("0")

    def _calculate_working_capital_changes(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Dict[str, float]:
        """Calculate changes in working capital components."""
        try:
            # For simplicity, we'll calculate basic working capital changes
            # In a real implementation, you'd compare beginning and ending balances

            # Accounts receivable change (negative = increase in AR, positive = decrease)
            ar_change = self._get_account_balance_change(
                organization_id, from_date, to_date, ["ACCOUNTS RECEIVABLE", "RECEIVABLES"]
            )

            # Inventory change (negative = increase in inventory, positive = decrease)
            inventory_change = self._get_account_balance_change(
                organization_id, from_date, to_date, ["INVENTORY", "STOCK"]
            )

            # Accounts payable change (positive = increase in AP, negative = decrease)
            ap_change = self._get_account_balance_change(
                organization_id, from_date, to_date, ["ACCOUNTS PAYABLE", "PAYABLES"]
            )

            # Accrued expenses change (positive = increase, negative = decrease)
            accrued_change = self._get_account_balance_change(
                organization_id, from_date, to_date, ["ACCRUED", "ACCRUALS"]
            )

            return {
                "accounts_receivable_change": float(self._round_currency(-ar_change)),
                "inventory_change": float(self._round_currency(-inventory_change)),
                "accounts_payable_change": float(self._round_currency(ap_change)),
                "accrued_expenses_change": float(self._round_currency(accrued_change)),
            }

        except Exception as e:
            logger.error(f"Error calculating working capital changes: {e}")
            return {
                "accounts_receivable_change": 0.0,
                "inventory_change": 0.0,
                "accounts_payable_change": 0.0,
                "accrued_expenses_change": 0.0,
            }

    def _get_account_balance_change(
        self, organization_id: int, from_date: datetime, to_date: datetime, account_types: List[str]
    ) -> Decimal:
        """Get the change in account balance for specified account types."""
        try:
            balance_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(*[Account.type.ilike(f"%{acc_type}%") for acc_type in account_types])
                )
            )
            balance_change = balance_query.scalar() or 0
            return self._round_currency(balance_change)

        except Exception as e:
            logger.error(f"Error getting account balance change: {e}")
            return Decimal("0")

    def _calculate_investing_activities(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Dict[str, Any]:
        """Calculate investing activities cash flows."""
        try:
            # Capital expenditures (purchases of property, plant, equipment)
            capex = self._get_capital_expenditures(organization_id, from_date, to_date)

            # Asset sales
            asset_sales = self._get_asset_sales(organization_id, from_date, to_date)

            # Investments in securities
            investment_purchases = self._get_investment_purchases(organization_id, from_date, to_date)
            investment_sales = self._get_investment_sales(organization_id, from_date, to_date)

            # Calculate net cash from investing activities
            net_cash_investing = asset_sales + investment_sales - capex - investment_purchases

            return {
                "capital_expenditures": float(self._round_currency(-capex)),
                "proceeds_from_asset_sales": float(self._round_currency(asset_sales)),
                "investment_purchases": float(self._round_currency(-investment_purchases)),
                "proceeds_from_investment_sales": float(self._round_currency(investment_sales)),
                "net_cash_from_investing": float(self._round_currency(net_cash_investing)),
            }

        except Exception as e:
            logger.error(f"Error calculating investing activities: {e}")
            raise

    def _calculate_financing_activities(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Dict[str, Any]:
        """Calculate financing activities cash flows."""
        try:
            # Debt transactions
            debt_proceeds = self._get_debt_proceeds(organization_id, from_date, to_date)
            debt_payments = self._get_debt_payments(organization_id, from_date, to_date)

            # Equity transactions
            equity_proceeds = self._get_equity_proceeds(organization_id, from_date, to_date)
            dividend_payments = self._get_dividend_payments(organization_id, from_date, to_date)

            # Calculate net cash from financing activities
            net_cash_financing = debt_proceeds + equity_proceeds - debt_payments - dividend_payments

            return {
                "proceeds_from_debt": float(self._round_currency(debt_proceeds)),
                "debt_payments": float(self._round_currency(-debt_payments)),
                "proceeds_from_equity": float(self._round_currency(equity_proceeds)),
                "dividend_payments": float(self._round_currency(-dividend_payments)),
                "net_cash_from_financing": float(self._round_currency(net_cash_financing)),
            }

        except Exception as e:
            logger.error(f"Error calculating financing activities: {e}")
            raise

    def _get_cash_balance(
        self, organization_id: int, date: datetime, beginning: bool = True
    ) -> Decimal:
        """Get cash balance at beginning or end of period."""
        try:
            # For beginning balance, get balance before the date
            # For ending balance, get balance up to the date
            if beginning:
                balance_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                    and_(
                        Transaction.organization_id == organization_id,
                        Transaction.date < date,
                        or_(
                            Account.type.ilike("%cash%"),
                            Account.type.ilike("%bank%"),
                            Account.name.ilike("%cash%"),
                            Account.name.ilike("%bank%")
                        )
                    )
                )
            else:
                balance_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                    and_(
                        Transaction.organization_id == organization_id,
                        Transaction.date <= date,
                        or_(
                            Account.type.ilike("%cash%"),
                            Account.type.ilike("%bank%"),
                            Account.name.ilike("%cash%"),
                            Account.name.ilike("%bank%")
                        )
                    )
                )

            balance = balance_query.scalar() or 0
            return self._round_currency(balance)

        except Exception as e:
            logger.error(f"Error getting cash balance: {e}")
            return Decimal("0")

    # Direct method helper methods
    def _get_cash_receipts_from_customers(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get cash receipts from customers for direct method."""
        try:
            receipts_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    Account.type.in_(["REVENUE", "SALES", "INCOME"]),
                    Transaction.amount > 0  # Only positive amounts (receipts)
                )
            )
            receipts = receipts_query.scalar() or 0
            return self._round_currency(receipts)

        except Exception as e:
            logger.error(f"Error getting cash receipts from customers: {e}")
            return Decimal("0")

    def _get_cash_payments_to_suppliers(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get cash payments to suppliers for direct method."""
        try:
            payments_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.type.in_(["EXPENSE", "COST", "COGS"]),
                        Account.name.ilike("%supplier%"),
                        Account.name.ilike("%vendor%")
                    ),
                    Transaction.amount < 0  # Only negative amounts (payments)
                )
            )
            payments = payments_query.scalar() or 0
            return self._round_currency(abs(payments))  # Return positive value

        except Exception as e:
            logger.error(f"Error getting cash payments to suppliers: {e}")
            return Decimal("0")

    def _get_cash_payments_to_employees(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get cash payments to employees for direct method."""
        try:
            payments_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%salary%"),
                        Account.name.ilike("%wage%"),
                        Account.name.ilike("%payroll%"),
                        Account.type == "PAYROLL"
                    ),
                    Transaction.amount < 0  # Only negative amounts (payments)
                )
            )
            payments = payments_query.scalar() or 0
            return self._round_currency(abs(payments))  # Return positive value

        except Exception as e:
            logger.error(f"Error getting cash payments to employees: {e}")
            return Decimal("0")

    def _get_interest_paid(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get interest paid for the period."""
        try:
            interest_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%interest%"),
                        Account.type == "INTEREST_EXPENSE"
                    ),
                    Transaction.amount < 0  # Only negative amounts (payments)
                )
            )
            interest = interest_query.scalar() or 0
            return self._round_currency(abs(interest))  # Return positive value

        except Exception as e:
            logger.error(f"Error getting interest paid: {e}")
            return Decimal("0")

    def _get_taxes_paid(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get taxes paid for the period."""
        try:
            tax_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%tax%"),
                        Account.type == "TAX_EXPENSE"
                    ),
                    Transaction.amount < 0  # Only negative amounts (payments)
                )
            )
            taxes = tax_query.scalar() or 0
            return self._round_currency(abs(taxes))  # Return positive value

        except Exception as e:
            logger.error(f"Error getting taxes paid: {e}")
            return Decimal("0")

    # Investing activities helper methods
    def _get_capital_expenditures(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get capital expenditures for the period."""
        try:
            capex_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%equipment%"),
                        Account.name.ilike("%property%"),
                        Account.name.ilike("%plant%"),
                        Account.name.ilike("%building%"),
                        Account.type.in_(["FIXED_ASSET", "CAPITAL_ASSET"])
                    ),
                    Transaction.amount < 0  # Only negative amounts (purchases)
                )
            )
            capex = capex_query.scalar() or 0
            return self._round_currency(abs(capex))  # Return positive value

        except Exception as e:
            logger.error(f"Error getting capital expenditures: {e}")
            return Decimal("0")

    def _get_asset_sales(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get proceeds from asset sales for the period."""
        try:
            # This would typically come from gain/loss on sale accounts
            sales_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%gain on sale%"),
                        Account.name.ilike("%asset sale%"),
                        Account.type == "ASSET_SALE"
                    ),
                    Transaction.amount > 0  # Only positive amounts (proceeds)
                )
            )
            sales = sales_query.scalar() or 0
            return self._round_currency(sales)

        except Exception as e:
            logger.error(f"Error getting asset sales: {e}")
            return Decimal("0")

    def _get_investment_purchases(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get investment purchases for the period."""
        try:
            investment_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%investment%"),
                        Account.name.ilike("%securities%"),
                        Account.type.in_(["INVESTMENT", "MARKETABLE_SECURITIES"])
                    ),
                    Transaction.amount < 0  # Only negative amounts (purchases)
                )
            )
            investments = investment_query.scalar() or 0
            return self._round_currency(abs(investments))  # Return positive value

        except Exception as e:
            logger.error(f"Error getting investment purchases: {e}")
            return Decimal("0")

    def _get_investment_sales(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get proceeds from investment sales for the period."""
        try:
            sales_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%investment%"),
                        Account.name.ilike("%securities%"),
                        Account.type.in_(["INVESTMENT", "MARKETABLE_SECURITIES"])
                    ),
                    Transaction.amount > 0  # Only positive amounts (sales)
                )
            )
            sales = sales_query.scalar() or 0
            return self._round_currency(sales)

        except Exception as e:
            logger.error(f"Error getting investment sales: {e}")
            return Decimal("0")

    # Financing activities helper methods
    def _get_debt_proceeds(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get proceeds from debt issuance for the period."""
        try:
            debt_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%loan%"),
                        Account.name.ilike("%debt%"),
                        Account.name.ilike("%borrowing%"),
                        Account.type.in_(["LONG_TERM_DEBT", "SHORT_TERM_DEBT", "LOAN"])
                    ),
                    Transaction.amount > 0  # Only positive amounts (proceeds)
                )
            )
            debt = debt_query.scalar() or 0
            return self._round_currency(debt)

        except Exception as e:
            logger.error(f"Error getting debt proceeds: {e}")
            return Decimal("0")

    def _get_debt_payments(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get debt payments for the period."""
        try:
            payments_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%loan%"),
                        Account.name.ilike("%debt%"),
                        Account.name.ilike("%borrowing%"),
                        Account.type.in_(["LONG_TERM_DEBT", "SHORT_TERM_DEBT", "LOAN"])
                    ),
                    Transaction.amount < 0  # Only negative amounts (payments)
                )
            )
            payments = payments_query.scalar() or 0
            return self._round_currency(abs(payments))  # Return positive value

        except Exception as e:
            logger.error(f"Error getting debt payments: {e}")
            return Decimal("0")

    def _get_equity_proceeds(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get proceeds from equity issuance for the period."""
        try:
            equity_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%equity%"),
                        Account.name.ilike("%stock%"),
                        Account.name.ilike("%share%"),
                        Account.type.in_(["EQUITY", "COMMON_STOCK", "PREFERRED_STOCK"])
                    ),
                    Transaction.amount > 0  # Only positive amounts (proceeds)
                )
            )
            equity = equity_query.scalar() or 0
            return self._round_currency(equity)

        except Exception as e:
            logger.error(f"Error getting equity proceeds: {e}")
            return Decimal("0")

    def _get_dividend_payments(
        self, organization_id: int, from_date: datetime, to_date: datetime
    ) -> Decimal:
        """Get dividend payments for the period."""
        try:
            dividend_query = self.db.query(func.sum(Transaction.amount)).join(Account).filter(
                and_(
                    Transaction.organization_id == organization_id,
                    Transaction.date >= from_date,
                    Transaction.date <= to_date,
                    or_(
                        Account.name.ilike("%dividend%"),
                        Account.name.ilike("%distribution%"),
                        Account.type == "DIVIDEND"
                    ),
                    Transaction.amount < 0  # Only negative amounts (payments)
                )
            )
            dividends = dividend_query.scalar() or 0
            return self._round_currency(abs(dividends))  # Return positive value

        except Exception as e:
            logger.error(f"Error getting dividend payments: {e}")
            return Decimal("0")

    def _calculate_cash_flow_analysis(self, cash_flow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate financial analysis and ratios for cash flow statement."""
        try:
            operating_cash = cash_flow_data.get("operating_activities", {}).get("net_cash_from_operating", 0)
            investing_cash = cash_flow_data.get("investing_activities", {}).get("net_cash_from_investing", 0)
            financing_cash = cash_flow_data.get("financing_activities", {}).get("net_cash_from_financing", 0)

            # Cash flow ratios and analysis
            analysis = {
                "cash_flow_ratios": {
                    "operating_cash_flow_ratio": self._calculate_operating_cash_flow_ratio(operating_cash),
                    "free_cash_flow": operating_cash + investing_cash,
                    "cash_coverage_ratio": self._calculate_cash_coverage_ratio(operating_cash),
                },
                "cash_flow_quality": {
                    "operating_vs_net_income": self._analyze_operating_vs_net_income(cash_flow_data),
                    "cash_conversion_cycle": self._calculate_cash_conversion_cycle(cash_flow_data),
                },
                "liquidity_analysis": {
                    "cash_burn_rate": self._calculate_cash_burn_rate(cash_flow_data),
                    "cash_runway": self._calculate_cash_runway(cash_flow_data),
                },
                "investment_analysis": {
                    "capex_intensity": self._calculate_capex_intensity(cash_flow_data),
                    "reinvestment_rate": self._calculate_reinvestment_rate(cash_flow_data),
                },
            }

            return analysis

        except Exception as e:
            logger.error(f"Error calculating cash flow analysis: {e}")
            return {}

    def _calculate_operating_cash_flow_ratio(self, operating_cash: float) -> float:
        """Calculate operating cash flow ratio."""
        # This would typically be operating cash flow / current liabilities
        # For now, return a simplified calculation
        return operating_cash / 1000 if operating_cash > 0 else 0

    def _calculate_cash_coverage_ratio(self, operating_cash: float) -> float:
        """Calculate cash coverage ratio."""
        # This would typically be (operating cash flow + interest + taxes) / interest
        # For now, return a simplified calculation
        return operating_cash / 100 if operating_cash > 0 else 0

    def _analyze_operating_vs_net_income(self, cash_flow_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze operating cash flow vs net income quality."""
        operating_cash = cash_flow_data.get("operating_activities", {}).get("net_cash_from_operating", 0)

        if cash_flow_data.get("method") == "indirect":
            net_income = cash_flow_data.get("operating_activities", {}).get("net_income", 0)
            quality_ratio = operating_cash / net_income if net_income != 0 else 0

            return {
                "quality_ratio": quality_ratio,
                "quality_assessment": (
                    "High" if quality_ratio > 1.2 else
                    "Good" if quality_ratio > 0.8 else
                    "Poor"
                ),
            }

        return {"quality_ratio": 0, "quality_assessment": "N/A"}

    def _calculate_cash_conversion_cycle(self, cash_flow_data: Dict[str, Any]) -> float:
        """Calculate cash conversion cycle (simplified)."""
        # This would typically involve days sales outstanding, days inventory outstanding, etc.
        # For now, return a placeholder
        return 30.0

    def _calculate_cash_burn_rate(self, cash_flow_data: Dict[str, Any]) -> float:
        """Calculate monthly cash burn rate."""
        operating_cash = cash_flow_data.get("operating_activities", {}).get("net_cash_from_operating", 0)
        # Convert to monthly rate (assuming quarterly data)
        return abs(operating_cash) / 3 if operating_cash < 0 else 0

    def _calculate_cash_runway(self, cash_flow_data: Dict[str, Any]) -> float:
        """Calculate cash runway in months."""
        ending_cash = cash_flow_data.get("cash_summary", {}).get("ending_cash", 0)
        burn_rate = self._calculate_cash_burn_rate(cash_flow_data)
        return ending_cash / burn_rate if burn_rate > 0 else float('inf')

    def _calculate_capex_intensity(self, cash_flow_data: Dict[str, Any]) -> float:
        """Calculate capital expenditure intensity."""
        capex = abs(cash_flow_data.get("investing_activities", {}).get("capital_expenditures", 0))
        operating_cash = cash_flow_data.get("operating_activities", {}).get("net_cash_from_operating", 0)
        return capex / operating_cash if operating_cash > 0 else 0

    def _calculate_reinvestment_rate(self, cash_flow_data: Dict[str, Any]) -> float:
        """Calculate reinvestment rate."""
        capex = abs(cash_flow_data.get("investing_activities", {}).get("capital_expenditures", 0))
        operating_cash = cash_flow_data.get("operating_activities", {}).get("net_cash_from_operating", 0)
        return capex / operating_cash if operating_cash > 0 else 0
