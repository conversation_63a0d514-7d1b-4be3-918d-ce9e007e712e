"""
Enhanced data processing module with GAAP compliance and NASDAQ reporting standards.
"""

from typing import Dict, Optional, Any
from decimal import Decimal, ROUND_HALF_UP
import logging
from enum import Enum

from mcx3d_finance.core.config import get_xero_config

logger = logging.getLogger(__name__)


class GAAPAccountClassification(Enum):
    """GAAP-compliant account classifications for NASDAQ reporting."""

    # Assets
    CURRENT_ASSETS = "current_assets"
    CASH_AND_EQUIVALENTS = "cash_and_equivalents"
    ACCOUNTS_RECEIVABLE = "accounts_receivable"
    INVENTORY = "inventory"
    PREPAID_EXPENSES = "prepaid_expenses"
    OTHER_CURRENT_ASSETS = "other_current_assets"

    NON_CURRENT_ASSETS = "non_current_assets"
    PROPERTY_PLANT_EQUIPMENT = "property_plant_equipment"
    INTANGIBLE_ASSETS = "intangible_assets"
    GOODWILL = "goodwill"
    INVESTMENTS = "investments"
    OTHER_NON_CURRENT_ASSETS = "other_non_current_assets"

    # Liabilities
    CURRENT_LIABILITIES = "current_liabilities"
    ACCOUNTS_PAYABLE = "accounts_payable"
    ACCRUED_LIABILITIES = "accrued_liabilities"
    SHORT_TERM_DEBT = "short_term_debt"
    CURRENT_PORTION_LONG_TERM_DEBT = "current_portion_long_term_debt"
    OTHER_CURRENT_LIABILITIES = "other_current_liabilities"

    NON_CURRENT_LIABILITIES = "non_current_liabilities"
    LONG_TERM_DEBT = "long_term_debt"
    DEFERRED_TAX_LIABILITIES = "deferred_tax_liabilities"
    OTHER_NON_CURRENT_LIABILITIES = "other_non_current_liabilities"

    # Equity
    STOCKHOLDERS_EQUITY = "stockholders_equity"
    COMMON_STOCK = "common_stock"
    RETAINED_EARNINGS = "retained_earnings"
    ADDITIONAL_PAID_IN_CAPITAL = "additional_paid_in_capital"
    ACCUMULATED_OTHER_COMPREHENSIVE_INCOME = "accumulated_other_comprehensive_income"

    # Revenue
    REVENUE = "revenue"
    PRODUCT_REVENUE = "product_revenue"
    SERVICE_REVENUE = "service_revenue"
    SUBSCRIPTION_REVENUE = "subscription_revenue"
    OTHER_REVENUE = "other_revenue"

    # Expenses
    COST_OF_REVENUE = "cost_of_revenue"
    COST_OF_GOODS_SOLD = "cost_of_goods_sold"
    COST_OF_SERVICES = "cost_of_services"

    OPERATING_EXPENSES = "operating_expenses"
    SALES_AND_MARKETING = "sales_and_marketing"
    RESEARCH_AND_DEVELOPMENT = "research_and_development"
    GENERAL_AND_ADMINISTRATIVE = "general_and_administrative"
    DEPRECIATION_AND_AMORTIZATION = "depreciation_and_amortization"

    NON_OPERATING_EXPENSES = "non_operating_expenses"
    INTEREST_EXPENSE = "interest_expense"
    OTHER_EXPENSE = "other_expense"


class XeroDataProcessor:
    """Enhanced Xero data processor with GAAP compliance and NASDAQ standards."""

    def __init__(self):
        self.xero_config = get_xero_config()
        self.gaap_mappings = self._load_gaap_account_mappings()
        self.precision = Decimal("0.01")

    def _load_gaap_account_mappings(self) -> Dict[str, GAAPAccountClassification]:
        """Load GAAP-compliant account mappings for NASDAQ reporting."""
        return {
            # Cash and Cash Equivalents
            "1010": GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            "1020": GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            "1030": GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            # Accounts Receivable
            "1200": GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
            "1210": GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
            # Inventory
            "1300": GAAPAccountClassification.INVENTORY,
            "1310": GAAPAccountClassification.INVENTORY,
            # Prepaid Expenses
            "1400": GAAPAccountClassification.PREPAID_EXPENSES,
            "1410": GAAPAccountClassification.PREPAID_EXPENSES,
            # Property, Plant & Equipment
            "1600": GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            "1610": GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            "1620": GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            # Intangible Assets
            "1700": GAAPAccountClassification.INTANGIBLE_ASSETS,
            "1710": GAAPAccountClassification.GOODWILL,
            # Accounts Payable
            "2000": GAAPAccountClassification.ACCOUNTS_PAYABLE,
            "2010": GAAPAccountClassification.ACCOUNTS_PAYABLE,
            # Accrued Liabilities
            "2100": GAAPAccountClassification.ACCRUED_LIABILITIES,
            "2110": GAAPAccountClassification.ACCRUED_LIABILITIES,
            # Short-term Debt
            "2200": GAAPAccountClassification.SHORT_TERM_DEBT,
            "2210": GAAPAccountClassification.CURRENT_PORTION_LONG_TERM_DEBT,
            # Long-term Debt
            "2400": GAAPAccountClassification.LONG_TERM_DEBT,
            "2410": GAAPAccountClassification.LONG_TERM_DEBT,
            # Equity
            "3000": GAAPAccountClassification.COMMON_STOCK,
            "3100": GAAPAccountClassification.RETAINED_EARNINGS,
            "3200": GAAPAccountClassification.ADDITIONAL_PAID_IN_CAPITAL,
            # Revenue
            "4000": GAAPAccountClassification.PRODUCT_REVENUE,
            "4100": GAAPAccountClassification.SERVICE_REVENUE,
            "4200": GAAPAccountClassification.SUBSCRIPTION_REVENUE,
            "4900": GAAPAccountClassification.OTHER_REVENUE,
            # Cost of Revenue
            "5000": GAAPAccountClassification.COST_OF_GOODS_SOLD,
            "5100": GAAPAccountClassification.COST_OF_SERVICES,
            # Operating Expenses
            "6000": GAAPAccountClassification.SALES_AND_MARKETING,
            "6100": GAAPAccountClassification.RESEARCH_AND_DEVELOPMENT,
            "6200": GAAPAccountClassification.GENERAL_AND_ADMINISTRATIVE,
            "6300": GAAPAccountClassification.DEPRECIATION_AND_AMORTIZATION,
            # Non-operating
            "7000": GAAPAccountClassification.INTEREST_EXPENSE,
            "7900": GAAPAccountClassification.OTHER_EXPENSE,
        }

    def _round_currency(self, value: float) -> Decimal:
        """Round currency values to 2 decimal places for GAAP compliance."""
        return Decimal(str(value)).quantize(self.precision, rounding=ROUND_HALF_UP)

    def process_trial_balance_for_gaap(
        self, trial_balance_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process trial balance data into GAAP-compliant format for NASDAQ reporting."""
        try:
            logger.info("Processing trial balance for GAAP compliance")

            gaap_balances = {
                "report_date": trial_balance_data["report_date"],
                "assets": {
                    "current_assets": {},
                    "non_current_assets": {},
                    "total_assets": Decimal("0"),
                },
                "liabilities": {
                    "current_liabilities": {},
                    "non_current_liabilities": {},
                    "total_liabilities": Decimal("0"),
                },
                "equity": {"stockholders_equity": {}, "total_equity": Decimal("0")},
            }

            for account in trial_balance_data.get("accounts", []):
                account_code = account.get("account_code", "")
                account_name = account.get("account_name", "")
                debit = self._round_currency(account.get("debit", 0))
                credit = self._round_currency(account.get("credit", 0))
                net_balance = debit - credit

                # Skip zero balances
                if net_balance == 0:
                    continue

                # Classify account using GAAP mapping
                gaap_classification = self._classify_account_gaap(account_code)

                if gaap_classification:
                    self._add_to_gaap_balance(
                        gaap_balances, gaap_classification, account_name, net_balance
                    )

            # Calculate totals
            self._calculate_gaap_totals(gaap_balances)

            # Validate balance sheet equation (Assets = Liabilities + Equity)
            self._validate_balance_sheet_equation(gaap_balances)

            return gaap_balances

        except Exception as e:
            logger.error(f"Error processing trial balance for GAAP: {e}")
            raise

    def process_profit_loss_for_gaap(self, pl_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process P&L data into GAAP-compliant income statement format."""
        try:
            logger.info("Processing P&L for GAAP compliance")

            gaap_income_statement = {
                "period_start": pl_data["from_date"],
                "period_end": pl_data["to_date"],
                "revenue": {"total_revenue": Decimal("0"), "breakdown": {}},
                "cost_of_revenue": {
                    "total_cost_of_revenue": Decimal("0"),
                    "breakdown": {},
                },
                "gross_profit": Decimal("0"),
                "operating_expenses": {
                    "total_operating_expenses": Decimal("0"),
                    "breakdown": {},
                },
                "operating_income": Decimal("0"),
                "non_operating_income_expense": {
                    "total_non_operating": Decimal("0"),
                    "breakdown": {},
                },
                "income_before_taxes": Decimal("0"),
                "income_tax_expense": Decimal("0"),
                "net_income": Decimal("0"),
            }

            # Process each section from Xero P&L
            for section in pl_data.get("sections", []):
                section_name = section.get("section_name", "").lower()

                for account in section.get("accounts", []):
                    account_name = account.get("account_name", "")
                    amount = self._round_currency(account.get("amount", 0))

                    # Classify and add to appropriate GAAP section
                    if "revenue" in section_name or "income" in section_name:
                        gaap_income_statement["revenue"]["breakdown"][account_name] = (
                            float(amount)
                        )
                        gaap_income_statement["revenue"]["total_revenue"] += amount
                    elif (
                        "cost of sales" in section_name
                        or "cost of goods" in section_name
                    ):
                        gaap_income_statement["cost_of_revenue"]["breakdown"][
                            account_name
                        ] = float(amount)
                        gaap_income_statement["cost_of_revenue"][
                            "total_cost_of_revenue"
                        ] += amount
                    elif "expense" in section_name:
                        gaap_income_statement["operating_expenses"]["breakdown"][
                            account_name
                        ] = float(amount)
                        gaap_income_statement["operating_expenses"][
                            "total_operating_expenses"
                        ] += amount

            # Calculate derived amounts
            gaap_income_statement["gross_profit"] = (
                gaap_income_statement["revenue"]["total_revenue"]
                - gaap_income_statement["cost_of_revenue"]["total_cost_of_revenue"]
            )

            gaap_income_statement["operating_income"] = (
                gaap_income_statement["gross_profit"]
                - gaap_income_statement["operating_expenses"][
                    "total_operating_expenses"
                ]
            )

            gaap_income_statement["income_before_taxes"] = (
                gaap_income_statement["operating_income"]
                + gaap_income_statement["non_operating_income_expense"][
                    "total_non_operating"
                ]
            )

            gaap_income_statement["net_income"] = (
                gaap_income_statement["income_before_taxes"]
                - gaap_income_statement["income_tax_expense"]
            )

            # Convert Decimal to float for JSON serialization
            self._convert_decimals_to_float(gaap_income_statement)

            return gaap_income_statement

        except Exception as e:
            logger.error(f"Error processing P&L for GAAP: {e}")
            raise

    def generate_nasdaq_financial_ratios(
        self, balance_sheet: Dict[str, Any], income_statement: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate NASDAQ-required financial ratios and metrics."""
        try:
            logger.info("Calculating NASDAQ financial ratios")

            # Extract key figures
            total_assets = Decimal(str(balance_sheet["assets"]["total_assets"]))
            total_liabilities = Decimal(
                str(balance_sheet["liabilities"]["total_liabilities"])
            )
            total_equity = Decimal(str(balance_sheet["equity"]["total_equity"]))
            current_assets = sum(
                Decimal(str(v))
                for v in balance_sheet["assets"]["current_assets"].values()
            )
            current_liabilities = sum(
                Decimal(str(v))
                for v in balance_sheet["liabilities"]["current_liabilities"].values()
            )

            total_revenue = Decimal(str(income_statement["revenue"]["total_revenue"]))
            gross_profit = Decimal(str(income_statement["gross_profit"]))
            operating_income = Decimal(str(income_statement["operating_income"]))
            net_income = Decimal(str(income_statement["net_income"]))

            ratios = {
                "liquidity_ratios": {
                    "current_ratio": (
                        float(current_assets / current_liabilities)
                        if current_liabilities > 0
                        else 0
                    ),
                    "quick_ratio": (
                        float(
                            (
                                current_assets
                                - Decimal(
                                    str(
                                        balance_sheet["assets"]["current_assets"].get(
                                            "inventory", 0
                                        )
                                    )
                                )
                            )
                            / current_liabilities
                        )
                        if current_liabilities > 0
                        else 0
                    ),
                    "cash_ratio": (
                        float(
                            Decimal(
                                str(
                                    balance_sheet["assets"]["current_assets"].get(
                                        "cash_and_equivalents", 0
                                    )
                                )
                            )
                            / current_liabilities
                        )
                        if current_liabilities > 0
                        else 0
                    ),
                },
                "leverage_ratios": {
                    "debt_to_equity": (
                        float(total_liabilities / total_equity)
                        if total_equity > 0
                        else 0
                    ),
                    "debt_to_assets": (
                        float(total_liabilities / total_assets)
                        if total_assets > 0
                        else 0
                    ),
                    "equity_ratio": (
                        float(total_equity / total_assets) if total_assets > 0 else 0
                    ),
                },
                "profitability_ratios": {
                    "gross_margin": (
                        float(gross_profit / total_revenue * 100)
                        if total_revenue > 0
                        else 0
                    ),
                    "operating_margin": (
                        float(operating_income / total_revenue * 100)
                        if total_revenue > 0
                        else 0
                    ),
                    "net_margin": (
                        float(net_income / total_revenue * 100)
                        if total_revenue > 0
                        else 0
                    ),
                    "return_on_assets": (
                        float(net_income / total_assets * 100)
                        if total_assets > 0
                        else 0
                    ),
                    "return_on_equity": (
                        float(net_income / total_equity * 100)
                        if total_equity > 0
                        else 0
                    ),
                },
                "efficiency_ratios": {
                    "asset_turnover": (
                        float(total_revenue / total_assets) if total_assets > 0 else 0
                    ),
                    "equity_turnover": (
                        float(total_revenue / total_equity) if total_equity > 0 else 0
                    ),
                },
            }

            return ratios

        except Exception as e:
            logger.error(f"Error calculating NASDAQ financial ratios: {e}")
            raise

    def _classify_account_gaap(
        self, account_code: str
    ) -> Optional[GAAPAccountClassification]:
        """Classify account using GAAP standards."""
        if not account_code:
            return None

        # Try exact match first
        if account_code in self.gaap_mappings:
            return self.gaap_mappings[account_code]

        # Try prefix matching
        for code_prefix, classification in self.gaap_mappings.items():
            if account_code.startswith(code_prefix):
                return classification

        return None

    def _add_to_gaap_balance(
        self,
        gaap_balances: Dict[str, Any],
        classification: GAAPAccountClassification,
        account_name: str,
        balance: Decimal,
    ):
        """Add account balance to appropriate GAAP section."""

        # Asset classifications
        if classification in [
            GAAPAccountClassification.CASH_AND_EQUIVALENTS,
            GAAPAccountClassification.ACCOUNTS_RECEIVABLE,
            GAAPAccountClassification.INVENTORY,
            GAAPAccountClassification.PREPAID_EXPENSES,
            GAAPAccountClassification.OTHER_CURRENT_ASSETS,
        ]:
            gaap_balances["assets"]["current_assets"][account_name] = float(balance)

        elif classification in [
            GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT,
            GAAPAccountClassification.INTANGIBLE_ASSETS,
            GAAPAccountClassification.GOODWILL,
            GAAPAccountClassification.INVESTMENTS,
            GAAPAccountClassification.OTHER_NON_CURRENT_ASSETS,
        ]:
            gaap_balances["assets"]["non_current_assets"][account_name] = float(balance)

        # Liability classifications
        elif classification in [
            GAAPAccountClassification.ACCOUNTS_PAYABLE,
            GAAPAccountClassification.ACCRUED_LIABILITIES,
            GAAPAccountClassification.SHORT_TERM_DEBT,
            GAAPAccountClassification.CURRENT_PORTION_LONG_TERM_DEBT,
            GAAPAccountClassification.OTHER_CURRENT_LIABILITIES,
        ]:
            gaap_balances["liabilities"]["current_liabilities"][account_name] = float(
                balance
            )

        elif classification in [
            GAAPAccountClassification.LONG_TERM_DEBT,
            GAAPAccountClassification.DEFERRED_TAX_LIABILITIES,
            GAAPAccountClassification.OTHER_NON_CURRENT_LIABILITIES,
        ]:
            gaap_balances["liabilities"]["non_current_liabilities"][account_name] = (
                float(balance)
            )

        # Equity classifications
        elif classification in [
            GAAPAccountClassification.COMMON_STOCK,
            GAAPAccountClassification.RETAINED_EARNINGS,
            GAAPAccountClassification.ADDITIONAL_PAID_IN_CAPITAL,
            GAAPAccountClassification.ACCUMULATED_OTHER_COMPREHENSIVE_INCOME,
        ]:
            gaap_balances["equity"]["stockholders_equity"][account_name] = float(
                balance
            )

    def _calculate_gaap_totals(self, gaap_balances: Dict[str, Any]):
        """Calculate totals for GAAP balance sheet."""
        # Calculate asset totals
        current_assets_total = sum(gaap_balances["assets"]["current_assets"].values())
        non_current_assets_total = sum(
            gaap_balances["assets"]["non_current_assets"].values()
        )
        gaap_balances["assets"]["total_assets"] = (
            current_assets_total + non_current_assets_total
        )

        # Calculate liability totals
        current_liabilities_total = sum(
            gaap_balances["liabilities"]["current_liabilities"].values()
        )
        non_current_liabilities_total = sum(
            gaap_balances["liabilities"]["non_current_liabilities"].values()
        )
        gaap_balances["liabilities"]["total_liabilities"] = (
            current_liabilities_total + non_current_liabilities_total
        )

        # Calculate equity total
        equity_total = sum(gaap_balances["equity"]["stockholders_equity"].values())
        gaap_balances["equity"]["total_equity"] = equity_total

    def _validate_balance_sheet_equation(self, gaap_balances: Dict[str, Any]):
        """Validate that Assets = Liabilities + Equity (fundamental accounting equation)."""
        total_assets = gaap_balances["assets"]["total_assets"]
        total_liabilities = gaap_balances["liabilities"]["total_liabilities"]
        total_equity = gaap_balances["equity"]["total_equity"]

        difference = abs(total_assets - (total_liabilities + total_equity))

        # Allow for small rounding differences (less than $1)
        if difference > 1.00:
            logger.warning(
                f"Balance sheet equation validation failed. Difference: ${difference:.2f}"
            )
            logger.warning(
                f"Assets: ${total_assets:.2f}, Liabilities + Equity: ${total_liabilities + total_equity:.2f}"
            )
        else:
            logger.info("Balance sheet equation validated successfully")

    def _convert_decimals_to_float(self, data: Any):
        """Recursively convert Decimal objects to float for JSON serialization."""
        if isinstance(data, dict):
            for key, value in data.items():
                data[key] = self._convert_decimals_to_float(value)
        elif isinstance(data, list):
            for i, item in enumerate(data):
                data[i] = self._convert_decimals_to_float(item)
        elif isinstance(data, Decimal):
            return float(data)
        return data
