#!/usr/bin/env python3
"""
Simple performance test for MCX3D Financial System Phase 1.
"""

import sys
import os
import time
from datetime import datetime, timedelta
from unittest.mock import Mock

# Add project root to path
sys.path.insert(0, os.path.abspath('.'))

def test_performance():
    """Test performance of all Phase 1 components."""
    print("⚡ MCX3D Financial System - Performance Test")
    print("=" * 50)
    
    try:
        # Import components
        from mcx3d_finance.core.financials.cash_flow import CashFlowGenerator
        from mcx3d_finance.core.valuation.dcf import DCFValuation
        from mcx3d_finance.core.valuation.multiples import MultiplesValuation
        from mcx3d_finance.core.metrics.saas_kpis import SaaSKPICalculator
        
        results = {}
        
        # Test Cash Flow Generator
        print("\n📊 Testing Cash Flow Generator...")
        mock_session = Mock()
        mock_org = Mock()
        mock_org.name = "Performance Test Corp"
        mock_org.id = 1
        mock_session.query().filter().first.return_value = mock_org
        mock_session.query().join().filter().scalar.return_value = 100000
        
        generator = CashFlowGenerator(mock_session)
        
        start_time = time.time()
        cash_flow_result = generator.generate_cash_flow_statement(
            organization_id=1,
            from_date=datetime(2024, 1, 1),
            to_date=datetime(2024, 12, 31),
            method="indirect"
        )
        cf_time = time.time() - start_time
        results['cash_flow'] = cf_time
        
        print(f"   ✅ Cash Flow: {cf_time:.3f}s")
        
        # Test DCF Valuation
        print("\n💰 Testing DCF Valuation...")
        dcf = DCFValuation()
        
        projections = [
            {"year": 1, "revenue": 1000000, "free_cash_flow": 300000},
            {"year": 2, "revenue": 1200000, "free_cash_flow": 380000},
            {"year": 3, "revenue": 1440000, "free_cash_flow": 480000},
        ]
        
        start_time = time.time()
        dcf_result = dcf.calculate_dcf_valuation(
            projections, 0.12, 0.03, ["base"]
        )
        dcf_time = time.time() - start_time
        results['dcf'] = dcf_time
        
        print(f"   ✅ DCF Valuation: {dcf_time:.3f}s")
        
        # Test Multiples Valuation
        print("\n📈 Testing Multiples Valuation...")
        multiples = MultiplesValuation()
        
        target_metrics = {
            "revenue": 10000000,
            "ebitda": 3000000,
            "net_income": 1500000,
        }
        
        comparable_companies = [
            {"company": "Comp A", "ev_revenue": 4.5, "ev_ebitda": 15.0, "pe_ratio": 25.0},
            {"company": "Comp B", "ev_revenue": 5.2, "ev_ebitda": 18.0, "pe_ratio": 30.0},
            {"company": "Comp C", "ev_revenue": 3.8, "ev_ebitda": 12.0, "pe_ratio": 22.0},
        ]
        
        start_time = time.time()
        multiples_result = multiples.calculate_comprehensive_multiples_valuation(
            target_metrics=target_metrics,
            comparable_companies=comparable_companies
        )
        multiples_time = time.time() - start_time
        results['multiples'] = multiples_time
        
        print(f"   ✅ Multiples Valuation: {multiples_time:.3f}s")
        
        # Test SaaS KPIs
        print("\n🚀 Testing SaaS KPIs...")
        calculator = SaaSKPICalculator()
        
        start_time = time.time()
        kpis_result = calculator.calculate_comprehensive_kpis(
            organization_id=1,
            period_start=datetime(2024, 1, 1),
            period_end=datetime(2024, 12, 31)
        )
        kpis_time = time.time() - start_time
        results['saas_kpis'] = kpis_time
        
        print(f"   ✅ SaaS KPIs: {kpis_time:.3f}s")
        
        # Performance Summary
        print("\n" + "=" * 50)
        print("📋 PERFORMANCE SUMMARY")
        print("=" * 50)
        
        total_time = sum(results.values())
        max_time = max(results.values())
        
        print(f"Total Execution Time: {total_time:.3f}s")
        print(f"Maximum Component Time: {max_time:.3f}s")
        
        # Check requirements
        requirement_met = max_time < 5.0
        
        if requirement_met:
            print("✅ Performance Requirement Met: All components < 5 seconds")
        else:
            print("⚠️  Performance Requirement Not Met: Some components > 5 seconds")
        
        # Individual component performance
        print(f"\nComponent Performance:")
        for component, exec_time in results.items():
            status = "✅" if exec_time < 5.0 else "⚠️"
            print(f"  {status} {component.replace('_', ' ').title()}: {exec_time:.3f}s")
        
        # Performance grade
        if max_time < 1.0:
            grade = "A+ (Excellent)"
        elif max_time < 2.0:
            grade = "A (Very Good)"
        elif max_time < 3.0:
            grade = "B (Good)"
        elif max_time < 5.0:
            grade = "C (Acceptable)"
        else:
            grade = "D (Needs Improvement)"
        
        print(f"\nPerformance Grade: {grade}")
        
        # Optimization recommendations
        print(f"\n💡 Optimization Recommendations:")
        if max_time > 2.0:
            print("  • Consider implementing caching for repeated calculations")
            print("  • Optimize database queries with proper indexing")
        if total_time > 5.0:
            print("  • Consider parallel processing for independent calculations")
        
        print("  • Use connection pooling for database operations")
        print("  • Implement Redis caching for expensive operations")
        
        return {
            'results': results,
            'total_time': total_time,
            'max_time': max_time,
            'requirement_met': requirement_met,
            'grade': grade
        }
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    test_performance()
