from setuptools import setup, find_packages

setup(
    name="mcx3d_finance",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[
        "fastapi",
        "uvicorn",
        "click",
        "sqlalchemy",
        "psycopg2-binary",
        "redis",
        "celery",
        "pandas",
        "numpy",
        "pydantic",
        "reportlab",
        "openpyxl",
        "plotly",
    ],
    entry_points={
        "console_scripts": [
            "mcx3d-finance=mcx3d_finance.cli.main:cli",
        ],
    },
)
