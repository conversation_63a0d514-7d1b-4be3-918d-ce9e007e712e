{".class": "MypyFile", "_fullname": "mcx3d_finance.cli.reports", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Account": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.db.models.Account", "kind": "Gdef"}, "ReportGenerator": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.reporting.generator.ReportGenerator", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "Transaction": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.db.models.Transaction", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.cli.reports.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.cli.reports.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.cli.reports.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.cli.reports.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.cli.reports.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.cli.reports.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "balance_sheet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["organization_id", "date", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mcx3d_finance.cli.reports.balance_sheet", "name": "balance_sheet", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mcx3d_finance.cli.reports.balance_sheet", "name": "balance_sheet", "type": "click.core.Command"}}}, "balance_sheet_calc": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.financials.balance_sheet", "kind": "Gdef"}, "cash_flow": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["organization_id", "period", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mcx3d_finance.cli.reports.cash_flow", "name": "cash_flow", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mcx3d_finance.cli.reports.cash_flow", "name": "cash_flow", "type": "click.core.Command"}}}, "cash_flow_calc": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.financials.cash_flow", "kind": "Gdef"}, "click": {".class": "SymbolTableNode", "cross_ref": "click", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "generate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mcx3d_finance.cli.reports.generate", "name": "generate", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mcx3d_finance.cli.reports.generate", "name": "generate", "type": "click.core.Group"}}}, "get_db": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.db.session.get_db", "kind": "Gdef"}, "get_transactions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["db", "organization_id", "start_date", "end_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.cli.reports.get_transactions", "name": "get_transactions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["db", "organization_id", "start_date", "end_date"], "arg_types": ["sqlalchemy.orm.session.Session", "builtins.int", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_transactions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "income_statement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["organization_id", "period", "format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mcx3d_finance.cli.reports.income_statement", "name": "income_statement", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mcx3d_finance.cli.reports.income_statement", "name": "income_statement", "type": "click.core.Command"}}}, "income_statement_calc": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.financials.income_statement", "kind": "Gdef"}, "parse_date": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.cli.reports.parse_date", "name": "parse_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["date"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_date", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_period": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["period"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.cli.reports.parse_period", "name": "parse_period", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["period"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_period", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/cli/reports.py"}