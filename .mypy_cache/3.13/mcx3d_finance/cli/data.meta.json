{"data_mtime": 1752777569, "dep_lines": [4, 5, 60, 89, 1, 2, 3, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 20, 20, 10, 5, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["mcx3d_finance.tasks.sync_tasks", "mcx3d_finance.core.config", "mcx3d_finance.tasks.celery_app", "mcx3d_finance.core.data_processors", "click", "typing", "logging", "builtins", "_frozen_importlib", "abc", "click.core", "mcx3d_finance.core", "types"], "hash": "956897e7b2b8ea88699fcb74f1916140577a7db5", "id": "mcx3d_finance.cli.data", "ignore_all": false, "interface_hash": "c302a01d18e95a71bdfce27d9676ef142f85d0f6", "mtime": 1752777567, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/cli/data.py", "plugin_data": null, "size": 3580, "suppressed": [], "version_id": "1.15.0"}