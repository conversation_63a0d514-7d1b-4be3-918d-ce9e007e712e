{"data_mtime": 1752763228, "dep_lines": [3, 3, 3, 1, 2, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["mcx3d_finance.core.valuation.dcf", "mcx3d_finance.core.valuation.multiples", "mcx3d_finance.core.valuation", "click", "json", "builtins", "_frozen_importlib", "abc", "click.core", "click.types", "typing"], "hash": "ec9a0fee68ef430b5f639b046a4f75b7c47afb24", "id": "mcx3d_finance.cli.valuation", "ignore_all": true, "interface_hash": "4561af378c3f21223630b98f30e0ab054e9ba65c", "mtime": 1752765970, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/cli/valuation.py", "plugin_data": null, "size": 1232, "suppressed": [], "version_id": "1.15.0"}