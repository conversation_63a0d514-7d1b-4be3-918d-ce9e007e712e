{"data_mtime": 1752777499, "dep_lines": [3, 3, 3, 2, 3, 26, 27, 28, 1, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 20, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["mcx3d_finance.core.financials.income_statement", "mcx3d_finance.core.financials.balance_sheet", "mcx3d_finance.core.financials.cash_flow", "mcx3d_finance.reporting.generator", "mcx3d_finance.core.financials", "mcx3d_finance.db.session", "mcx3d_finance.db.models", "sqlalchemy.orm", "click", "datetime", "builtins", "_frozen_importlib", "abc", "click.core", "click.types", "sqlalchemy", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.orm.session", "typing"], "hash": "7ad7cf0e115db03f8a6b5c5af69c53f396df247f", "id": "mcx3d_finance.cli.reports", "ignore_all": true, "interface_hash": "43ad3fb306de532dabbd358240fbe542cb828168", "mtime": 1752776331, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/cli/reports.py", "plugin_data": null, "size": 4921, "suppressed": [], "version_id": "1.15.0"}