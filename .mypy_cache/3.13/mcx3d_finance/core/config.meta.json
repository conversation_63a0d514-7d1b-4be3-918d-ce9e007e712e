{"data_mtime": 1752781220, "dep_lines": [1, 3, 7, 5, 8, 1, 1, 1, 1, 1, 1, 1, 2], "dep_prios": [10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["os", "typing", "pydantic", "pydantic_settings", "logging", "builtins", "_frozen_importlib", "abc", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings.main"], "hash": "ded106bb0dc103ed93dcc993974b7bd654276311", "id": "mcx3d_finance.core.config", "ignore_all": true, "interface_hash": "fdd44155df45b30c6560bd566a2ea7540995ac39", "mtime": 1752781067, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/core/config.py", "plugin_data": null, "size": 2205, "suppressed": ["yaml"], "version_id": "1.15.0"}