{"data_mtime": 1752779395, "dep_lines": [10, 11, 5, 6, 7, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["mcx3d_finance.core.data_processors", "mcx3d_finance.integrations.xero_client", "logging", "typing", "datetime", "decimal", "builtins", "_frozen_importlib", "abc", "mcx3d_finance.integrations"], "hash": "d2e7525e954fa7fe5394d8f45e2f3ab75fe5445f", "id": "mcx3d_finance.core.financials.income_statement", "ignore_all": true, "interface_hash": "d29f8a8547d2b56ebd5b3e5af04a58c484e791ab", "mtime": 1752779193, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/core/financials/income_statement.py", "plugin_data": null, "size": 25427, "suppressed": [], "version_id": "1.15.0"}