{".class": "MypyFile", "_fullname": "mcx3d_finance.core.financials.balance_sheet", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BalanceSheetGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator", "name": "BalanceSheetGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mcx3d_finance.core.financials.balance_sheet", "mro": ["mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "arg_types": ["mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BalanceSheetGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_compliance_certifications": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator._add_compliance_certifications", "name": "_add_compliance_certifications", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_compliance_certifications of BalanceSheetGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_amount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator._format_amount", "name": "_format_amount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "amount"], "arg_types": ["mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_amount of BalanceSheetGenerator", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_for_nasdaq": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "gaap_data", "as_of_date", "comparative_data", "comparative_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator._format_for_nasdaq", "name": "_format_for_nasdaq", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "gaap_data", "as_of_date", "comparative_data", "comparative_date"], "arg_types": ["mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "datetime.datetime", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_for_nasdaq of BalanceSheetGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_balance_sheet_analysis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "balance_sheet"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator._generate_balance_sheet_analysis", "name": "_generate_balance_sheet_analysis", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "balance_sheet"], "arg_types": ["mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_balance_sheet_analysis of BalanceSheetGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_comparative_amount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "comparative_data", "section", "subsection", "keyword"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator._get_comparative_amount", "name": "_get_comparative_amount", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "comparative_data", "section", "subsection", "keyword"], "arg_types": ["mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_comparative_amount of BalanceSheetGenerator", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data_processor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator.data_processor", "name": "data_processor", "type": "mcx3d_finance.core.data_processors.XeroDataProcessor"}}, "generate_balance_sheet": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "as_of_date", "comparative_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator.generate_balance_sheet", "name": "generate_balance_sheet", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "as_of_date", "comparative_date"], "arg_types": ["mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator", "datetime.datetime", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_balance_sheet of BalanceSheetGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "organization_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator.organization_id", "name": "organization_id", "type": "builtins.int"}}, "precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator.precision", "name": "precision", "type": "decimal.Decimal"}}, "xero_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator.xero_client", "name": "xero_client", "type": "mcx3d_finance.integrations.xero_client.XeroClient"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mcx3d_finance.core.financials.balance_sheet.BalanceSheetGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GAAPAccountClassification": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.data_processors.GAAPAccountClassification", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ROUND_HALF_UP": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_HALF_UP", "kind": "Gdef"}, "XeroClient": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.integrations.xero_client.XeroClient", "kind": "Gdef"}, "XeroDataProcessor": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.data_processors.XeroDataProcessor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.balance_sheet.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.balance_sheet.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.balance_sheet.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.balance_sheet.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.balance_sheet.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.balance_sheet.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.financials.balance_sheet.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/core/financials/balance_sheet.py"}