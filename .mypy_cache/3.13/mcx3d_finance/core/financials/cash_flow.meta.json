{"data_mtime": 1752778502, "dep_lines": [5, 6, 7, 8, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30], "dependencies": ["typing", "datetime", "decimal", "logging", "builtins", "_frozen_importlib", "abc"], "hash": "93305aeb3506372861243ef8b811e0dd299d9abd", "id": "mcx3d_finance.core.financials.cash_flow", "ignore_all": true, "interface_hash": "7a940e9903ce05073d5fe1ac7a944a1bb034ae1b", "mtime": 1752778270, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/core/financials/cash_flow.py", "plugin_data": null, "size": 2330, "suppressed": [], "version_id": "1.15.0"}