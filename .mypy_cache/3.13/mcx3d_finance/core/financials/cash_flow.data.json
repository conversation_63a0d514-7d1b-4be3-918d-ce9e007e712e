{".class": "MypyFile", "_fullname": "mcx3d_finance.core.financials.cash_flow", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Account": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.db.models.Account", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "CashFlowGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "name": "CashFlowGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mcx3d_finance.core.financials.cash_flow", "mro": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "db_session"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "db_session"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "sqlalchemy.orm.session.Session"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CashFlowGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_analyze_operating_vs_net_income": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._analyze_operating_vs_net_income", "name": "_analyze_operating_vs_net_income", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_analyze_operating_vs_net_income of CashFlowGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_capex_intensity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_capex_intensity", "name": "_calculate_capex_intensity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_capex_intensity of CashFlowGenerator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_cash_burn_rate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_cash_burn_rate", "name": "_calculate_cash_burn_rate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_cash_burn_rate of CashFlowGenerator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_cash_conversion_cycle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_cash_conversion_cycle", "name": "_calculate_cash_conversion_cycle", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_cash_conversion_cycle of CashFlowGenerator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_cash_coverage_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operating_cash"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_cash_coverage_ratio", "name": "_calculate_cash_coverage_ratio", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operating_cash"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_cash_coverage_ratio of CashFlowGenerator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_cash_flow_analysis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_cash_flow_analysis", "name": "_calculate_cash_flow_analysis", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_cash_flow_analysis of CashFlowGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_cash_runway": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_cash_runway", "name": "_calculate_cash_runway", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_cash_runway of CashFlowGenerator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_financing_activities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_financing_activities", "name": "_calculate_financing_activities", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_financing_activities of CashFlowGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_investing_activities": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_investing_activities", "name": "_calculate_investing_activities", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_investing_activities of CashFlowGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_operating_activities_direct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_operating_activities_direct", "name": "_calculate_operating_activities_direct", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_operating_activities_direct of CashFlowGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_operating_activities_indirect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date", "net_income"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_operating_activities_indirect", "name": "_calculate_operating_activities_indirect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date", "net_income"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime", "decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_operating_activities_indirect of CashFlowGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_operating_cash_flow_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operating_cash"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_operating_cash_flow_ratio", "name": "_calculate_operating_cash_flow_ratio", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operating_cash"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_operating_cash_flow_ratio of CashFlowGenerator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_reinvestment_rate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_reinvestment_rate", "name": "_calculate_reinvestment_rate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cash_flow_data"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_reinvestment_rate of CashFlowGenerator", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_working_capital_changes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._calculate_working_capital_changes", "name": "_calculate_working_capital_changes", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_working_capital_changes of CashFlowGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_comparative_period": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._format_comparative_period", "name": "_format_comparative_period", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_comparative_period of CashFlowGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_period": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._format_period", "name": "_format_period", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_period of CashFlowGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_direct_cash_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "organization_id", "from_date", "to_date", "include_comparative"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._generate_direct_cash_flow", "name": "_generate_direct_cash_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "organization_id", "from_date", "to_date", "include_comparative"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_direct_cash_flow of CashFlowGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_indirect_cash_flow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "organization_id", "from_date", "to_date", "include_comparative"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._generate_indirect_cash_flow", "name": "_generate_indirect_cash_flow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "organization_id", "from_date", "to_date", "include_comparative"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_indirect_cash_flow of CashFlowGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_account_balance_change": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date", "account_types"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_account_balance_change", "name": "_get_account_balance_change", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date", "account_types"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_account_balance_change of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_asset_sales": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_asset_sales", "name": "_get_asset_sales", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_asset_sales of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_capital_expenditures": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_capital_expenditures", "name": "_get_capital_expenditures", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_capital_expenditures of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cash_balance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "organization_id", "date", "beginning"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_cash_balance", "name": "_get_cash_balance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "organization_id", "date", "beginning"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_cash_balance of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cash_payments_to_employees": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_cash_payments_to_employees", "name": "_get_cash_payments_to_employees", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_cash_payments_to_employees of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cash_payments_to_suppliers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_cash_payments_to_suppliers", "name": "_get_cash_payments_to_suppliers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_cash_payments_to_suppliers of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_cash_receipts_from_customers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_cash_receipts_from_customers", "name": "_get_cash_receipts_from_customers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_cash_receipts_from_customers of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_debt_payments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_debt_payments", "name": "_get_debt_payments", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_debt_payments of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_debt_proceeds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_debt_proceeds", "name": "_get_debt_proceeds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_debt_proceeds of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_depreciation_expense": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_depreciation_expense", "name": "_get_depreciation_expense", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_depreciation_expense of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_dividend_payments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_dividend_payments", "name": "_get_dividend_payments", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_dividend_payments of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_equity_proceeds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_equity_proceeds", "name": "_get_equity_proceeds", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_equity_proceeds of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_interest_paid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_interest_paid", "name": "_get_interest_paid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_interest_paid of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_investment_purchases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_investment_purchases", "name": "_get_investment_purchases", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_investment_purchases of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_investment_sales": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_investment_sales", "name": "_get_investment_sales", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_investment_sales of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_net_income": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_net_income", "name": "_get_net_income", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_net_income of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_organization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_organization", "name": "_get_organization", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_organization of CashFlowGenerator", "ret_type": "mcx3d_finance.db.models.Organization", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_taxes_paid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._get_taxes_paid", "name": "_get_taxes_paid", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "organization_id", "from_date", "to_date"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_taxes_paid of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_round_currency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator._round_currency", "name": "_round_currency", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_round_currency of CashFlowGenerator", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "db": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator.db", "name": "db", "type": "sqlalchemy.orm.session.Session"}}, "financial_calc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator.financial_calc", "name": "financial_calc", "type": "mcx3d_finance.core.financial_calculators.FinancialCalculator"}}, "generate_cash_flow_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "organization_id", "from_date", "to_date", "method", "include_comparative"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator.generate_cash_flow_statement", "name": "generate_cash_flow_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": ["self", "organization_id", "from_date", "to_date", "method", "include_comparative"], "arg_types": ["mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "builtins.int", "datetime.datetime", "datetime.datetime", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_cash_flow_statement of CashFlowGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator.precision", "name": "precision", "type": "decimal.Decimal"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mcx3d_finance.core.financials.cash_flow.CashFlowGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FinancialCalculator": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.financial_calculators.FinancialCalculator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Organization": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.db.models.Organization", "kind": "Gdef"}, "ROUND_HALF_UP": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_HALF_UP", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "Transaction": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.db.models.Transaction", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.cash_flow.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.cash_flow.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.cash_flow.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.cash_flow.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.cash_flow.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.cash_flow.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "and_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.and_", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "func": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.func", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.financials.cash_flow.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "or_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.or_", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/core/financials/cash_flow.py"}