{"data_mtime": 1752779395, "dep_lines": [10, 11, 5, 6, 7, 8, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["mcx3d_finance.core.data_processors", "mcx3d_finance.integrations.xero_client", "logging", "typing", "datetime", "decimal", "builtins", "_frozen_importlib", "abc", "mcx3d_finance.integrations"], "hash": "d7b561be5bb6144b82f258a19df4b80ed2708117", "id": "mcx3d_finance.core.financials.balance_sheet", "ignore_all": true, "interface_hash": "81071f908eb60fa45b140fa6c96e5a3168d18f01", "mtime": 1752779188, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/core/financials/balance_sheet.py", "plugin_data": null, "size": 31373, "suppressed": [], "version_id": "1.15.0"}