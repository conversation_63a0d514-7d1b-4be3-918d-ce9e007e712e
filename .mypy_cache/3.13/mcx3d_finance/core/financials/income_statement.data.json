{".class": "MypyFile", "_fullname": "mcx3d_finance.core.financials.income_statement", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "IncomeStatementGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator", "name": "IncomeStatementGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mcx3d_finance.core.financials.income_statement", "mro": ["mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "arg_types": ["mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IncomeStatementGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_compliance_certifications": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator._add_compliance_certifications", "name": "_add_compliance_certifications", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_compliance_certifications of IncomeStatementGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_earnings_per_share": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "income_statement"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator._calculate_earnings_per_share", "name": "_calculate_earnings_per_share", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "income_statement"], "arg_types": ["mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_earnings_per_share of IncomeStatementGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_amount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator._format_amount", "name": "_format_amount", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "amount"], "arg_types": ["mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_amount of IncomeStatementGenerator", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_for_nasdaq": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "gaap_data", "from_date", "to_date", "comparative_data", "comparative_from", "comparative_to"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator._format_for_nasdaq", "name": "_format_for_nasdaq", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "gaap_data", "from_date", "to_date", "comparative_data", "comparative_from", "comparative_to"], "arg_types": ["mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "datetime.datetime", "datetime.datetime", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_for_nasdaq of IncomeStatementGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_income_statement_analysis": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "income_statement"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator._generate_income_statement_analysis", "name": "_generate_income_statement_analysis", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "income_statement"], "arg_types": ["mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_income_statement_analysis of IncomeStatementGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "data_processor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator.data_processor", "name": "data_processor", "type": "mcx3d_finance.core.data_processors.XeroDataProcessor"}}, "generate_income_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "from_date", "to_date", "comparative_from", "comparative_to"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator.generate_income_statement", "name": "generate_income_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "from_date", "to_date", "comparative_from", "comparative_to"], "arg_types": ["mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator", "datetime.datetime", "datetime.datetime", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_income_statement of IncomeStatementGenerator", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "organization_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator.organization_id", "name": "organization_id", "type": "builtins.int"}}, "precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator.precision", "name": "precision", "type": "decimal.Decimal"}}, "xero_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator.xero_client", "name": "xero_client", "type": "mcx3d_finance.integrations.xero_client.XeroClient"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mcx3d_finance.core.financials.income_statement.IncomeStatementGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "XeroClient": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.integrations.xero_client.XeroClient", "kind": "Gdef"}, "XeroDataProcessor": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.data_processors.XeroDataProcessor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.income_statement.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.income_statement.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.income_statement.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.income_statement.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.income_statement.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.financials.income_statement.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.financials.income_statement.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/core/financials/income_statement.py"}