{".class": "MypyFile", "_fullname": "mcx3d_finance.core.data_processors", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "GAAPAccountClassification": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification", "name": "GAAPAccountClassification", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "mcx3d_finance.core.data_processors", "mro": ["mcx3d_finance.core.data_processors.GAAPAccountClassification", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ACCOUNTS_PAYABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.ACCOUNTS_PAYABLE", "name": "ACCOUNTS_PAYABLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "accounts_payable"}, "type_ref": "builtins.str"}}}, "ACCOUNTS_RECEIVABLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.ACCOUNTS_RECEIVABLE", "name": "ACCOUNTS_RECEIVABLE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "accounts_receivable"}, "type_ref": "builtins.str"}}}, "ACCRUED_LIABILITIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.ACCRUED_LIABILITIES", "name": "ACCRUED_LIABILITIES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "accrued_liabilities"}, "type_ref": "builtins.str"}}}, "ACCUMULATED_OTHER_COMPREHENSIVE_INCOME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.ACCUMULATED_OTHER_COMPREHENSIVE_INCOME", "name": "ACCUMULATED_OTHER_COMPREHENSIVE_INCOME", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "accumulated_other_comprehensive_income"}, "type_ref": "builtins.str"}}}, "ADDITIONAL_PAID_IN_CAPITAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.ADDITIONAL_PAID_IN_CAPITAL", "name": "ADDITIONAL_PAID_IN_CAPITAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "additional_paid_in_capital"}, "type_ref": "builtins.str"}}}, "CASH_AND_EQUIVALENTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.CASH_AND_EQUIVALENTS", "name": "CASH_AND_EQUIVALENTS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "cash_and_equivalents"}, "type_ref": "builtins.str"}}}, "COMMON_STOCK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.COMMON_STOCK", "name": "COMMON_STOCK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "common_stock"}, "type_ref": "builtins.str"}}}, "COST_OF_GOODS_SOLD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.COST_OF_GOODS_SOLD", "name": "COST_OF_GOODS_SOLD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "cost_of_goods_sold"}, "type_ref": "builtins.str"}}}, "COST_OF_REVENUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.COST_OF_REVENUE", "name": "COST_OF_REVENUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "cost_of_revenue"}, "type_ref": "builtins.str"}}}, "COST_OF_SERVICES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.COST_OF_SERVICES", "name": "COST_OF_SERVICES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "cost_of_services"}, "type_ref": "builtins.str"}}}, "CURRENT_ASSETS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.CURRENT_ASSETS", "name": "CURRENT_ASSETS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "current_assets"}, "type_ref": "builtins.str"}}}, "CURRENT_LIABILITIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.CURRENT_LIABILITIES", "name": "CURRENT_LIABILITIES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "current_liabilities"}, "type_ref": "builtins.str"}}}, "CURRENT_PORTION_LONG_TERM_DEBT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.CURRENT_PORTION_LONG_TERM_DEBT", "name": "CURRENT_PORTION_LONG_TERM_DEBT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "current_portion_long_term_debt"}, "type_ref": "builtins.str"}}}, "DEFERRED_TAX_LIABILITIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.DEFERRED_TAX_LIABILITIES", "name": "DEFERRED_TAX_LIABILITIES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "deferred_tax_liabilities"}, "type_ref": "builtins.str"}}}, "DEPRECIATION_AND_AMORTIZATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.DEPRECIATION_AND_AMORTIZATION", "name": "DEPRECIATION_AND_AMORTIZATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "depreciation_and_amortization"}, "type_ref": "builtins.str"}}}, "GENERAL_AND_ADMINISTRATIVE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.GENERAL_AND_ADMINISTRATIVE", "name": "GENERAL_AND_ADMINISTRATIVE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "general_and_administrative"}, "type_ref": "builtins.str"}}}, "GOODWILL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.GOODWILL", "name": "GOODWILL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "goodwill"}, "type_ref": "builtins.str"}}}, "INTANGIBLE_ASSETS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.INTANGIBLE_ASSETS", "name": "INTANGIBLE_ASSETS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "intangible_assets"}, "type_ref": "builtins.str"}}}, "INTEREST_EXPENSE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.INTEREST_EXPENSE", "name": "INTEREST_EXPENSE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "interest_expense"}, "type_ref": "builtins.str"}}}, "INVENTORY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.INVENTORY", "name": "INVENTORY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "inventory"}, "type_ref": "builtins.str"}}}, "INVESTMENTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.INVESTMENTS", "name": "INVESTMENTS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "investments"}, "type_ref": "builtins.str"}}}, "LONG_TERM_DEBT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.LONG_TERM_DEBT", "name": "LONG_TERM_DEBT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "long_term_debt"}, "type_ref": "builtins.str"}}}, "NON_CURRENT_ASSETS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.NON_CURRENT_ASSETS", "name": "NON_CURRENT_ASSETS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "non_current_assets"}, "type_ref": "builtins.str"}}}, "NON_CURRENT_LIABILITIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.NON_CURRENT_LIABILITIES", "name": "NON_CURRENT_LIABILITIES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "non_current_liabilities"}, "type_ref": "builtins.str"}}}, "NON_OPERATING_EXPENSES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.NON_OPERATING_EXPENSES", "name": "NON_OPERATING_EXPENSES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "non_operating_expenses"}, "type_ref": "builtins.str"}}}, "OPERATING_EXPENSES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.OPERATING_EXPENSES", "name": "OPERATING_EXPENSES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "operating_expenses"}, "type_ref": "builtins.str"}}}, "OTHER_CURRENT_ASSETS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.OTHER_CURRENT_ASSETS", "name": "OTHER_CURRENT_ASSETS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "other_current_assets"}, "type_ref": "builtins.str"}}}, "OTHER_CURRENT_LIABILITIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.OTHER_CURRENT_LIABILITIES", "name": "OTHER_CURRENT_LIABILITIES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "other_current_liabilities"}, "type_ref": "builtins.str"}}}, "OTHER_EXPENSE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.OTHER_EXPENSE", "name": "OTHER_EXPENSE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "other_expense"}, "type_ref": "builtins.str"}}}, "OTHER_NON_CURRENT_ASSETS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.OTHER_NON_CURRENT_ASSETS", "name": "OTHER_NON_CURRENT_ASSETS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "other_non_current_assets"}, "type_ref": "builtins.str"}}}, "OTHER_NON_CURRENT_LIABILITIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.OTHER_NON_CURRENT_LIABILITIES", "name": "OTHER_NON_CURRENT_LIABILITIES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "other_non_current_liabilities"}, "type_ref": "builtins.str"}}}, "OTHER_REVENUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.OTHER_REVENUE", "name": "OTHER_REVENUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "other_revenue"}, "type_ref": "builtins.str"}}}, "PREPAID_EXPENSES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.PREPAID_EXPENSES", "name": "PREPAID_EXPENSES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "prepaid_expenses"}, "type_ref": "builtins.str"}}}, "PRODUCT_REVENUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.PRODUCT_REVENUE", "name": "PRODUCT_REVENUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "product_revenue"}, "type_ref": "builtins.str"}}}, "PROPERTY_PLANT_EQUIPMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.PROPERTY_PLANT_EQUIPMENT", "name": "PROPERTY_PLANT_EQUIPMENT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "property_plant_equipment"}, "type_ref": "builtins.str"}}}, "RESEARCH_AND_DEVELOPMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.RESEARCH_AND_DEVELOPMENT", "name": "RESEARCH_AND_DEVELOPMENT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "research_and_development"}, "type_ref": "builtins.str"}}}, "RETAINED_EARNINGS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.RETAINED_EARNINGS", "name": "RETAINED_EARNINGS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "retained_earnings"}, "type_ref": "builtins.str"}}}, "REVENUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.REVENUE", "name": "REVENUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "revenue"}, "type_ref": "builtins.str"}}}, "SALES_AND_MARKETING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.SALES_AND_MARKETING", "name": "SALES_AND_MARKETING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "sales_and_marketing"}, "type_ref": "builtins.str"}}}, "SERVICE_REVENUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.SERVICE_REVENUE", "name": "SERVICE_REVENUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "service_revenue"}, "type_ref": "builtins.str"}}}, "SHORT_TERM_DEBT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.SHORT_TERM_DEBT", "name": "SHORT_TERM_DEBT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "short_term_debt"}, "type_ref": "builtins.str"}}}, "STOCKHOLDERS_EQUITY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.STOCKHOLDERS_EQUITY", "name": "STOCKHOLDERS_EQUITY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "stockholders_equity"}, "type_ref": "builtins.str"}}}, "SUBSCRIPTION_REVENUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.SUBSCRIPTION_REVENUE", "name": "SUBSCRIPTION_REVENUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "subscription_revenue"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mcx3d_finance.core.data_processors.GAAPAccountClassification.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mcx3d_finance.core.data_processors.GAAPAccountClassification", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ROUND_HALF_UP": {".class": "SymbolTableNode", "cross_ref": "_decimal.ROUND_HALF_UP", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "XeroDataProcessor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor", "name": "XeroDataProcessor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mcx3d_finance.core.data_processors", "mro": ["mcx3d_finance.core.data_processors.XeroDataProcessor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor.__init__", "name": "__init__", "type": null}}, "_add_to_gaap_balance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "gaap_balances", "classification", "account_name", "balance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor._add_to_gaap_balance", "name": "_add_to_gaap_balance", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "gaap_balances", "classification", "account_name", "balance"], "arg_types": ["mcx3d_finance.core.data_processors.XeroDataProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "mcx3d_finance.core.data_processors.GAAPAccountClassification", "builtins.str", "decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_to_gaap_balance of XeroDataProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_calculate_gaap_totals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gaap_balances"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor._calculate_gaap_totals", "name": "_calculate_gaap_totals", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "gaap_balances"], "arg_types": ["mcx3d_finance.core.data_processors.XeroDataProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_calculate_gaap_totals of XeroDataProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_classify_account_gaap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "account_code"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor._classify_account_gaap", "name": "_classify_account_gaap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "account_code"], "arg_types": ["mcx3d_finance.core.data_processors.XeroDataProcessor", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_classify_account_gaap of XeroDataProcessor", "ret_type": {".class": "UnionType", "items": ["mcx3d_finance.core.data_processors.GAAPAccountClassification", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_convert_decimals_to_float": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor._convert_decimals_to_float", "name": "_convert_decimals_to_float", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["mcx3d_finance.core.data_processors.XeroDataProcessor", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_convert_decimals_to_float of XeroDataProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_gaap_account_mappings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor._load_gaap_account_mappings", "name": "_load_gaap_account_mappings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mcx3d_finance.core.data_processors.XeroDataProcessor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_gaap_account_mappings of XeroDataProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", "mcx3d_finance.core.data_processors.GAAPAccountClassification"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_round_currency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor._round_currency", "name": "_round_currency", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["mcx3d_finance.core.data_processors.XeroDataProcessor", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_round_currency of XeroDataProcessor", "ret_type": "decimal.Decimal", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_balance_sheet_equation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "gaap_balances"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor._validate_balance_sheet_equation", "name": "_validate_balance_sheet_equation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "gaap_balances"], "arg_types": ["mcx3d_finance.core.data_processors.XeroDataProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_balance_sheet_equation of XeroDataProcessor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gaap_mappings": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor.gaap_mappings", "name": "gaap_mappings", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "generate_nasdaq_financial_ratios": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "balance_sheet", "income_statement"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor.generate_nasdaq_financial_ratios", "name": "generate_nasdaq_financial_ratios", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "balance_sheet", "income_statement"], "arg_types": ["mcx3d_finance.core.data_processors.XeroDataProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_nasdaq_financial_ratios of XeroDataProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "precision": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor.precision", "name": "precision", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "process_profit_loss_for_gaap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pl_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor.process_profit_loss_for_gaap", "name": "process_profit_loss_for_gaap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pl_data"], "arg_types": ["mcx3d_finance.core.data_processors.XeroDataProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_profit_loss_for_gaap of XeroDataProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_trial_balance_for_gaap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "trial_balance_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor.process_trial_balance_for_gaap", "name": "process_trial_balance_for_gaap", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "trial_balance_data"], "arg_types": ["mcx3d_finance.core.data_processors.XeroDataProcessor", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_trial_balance_for_gaap of XeroDataProcessor", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "xero_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor.xero_config", "name": "xero_config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mcx3d_finance.core.data_processors.XeroDataProcessor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mcx3d_finance.core.data_processors.XeroDataProcessor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.data_processors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.data_processors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.data_processors.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.data_processors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.data_processors.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.core.data_processors.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "get_xero_config": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.config.get_xero_config", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.core.data_processors.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "pd": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.core.data_processors.pd", "name": "pd", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.core.data_processors.pd", "source_any": null, "type_of_any": 3}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/core/data_processors.py"}