{"data_mtime": 1752779395, "dep_lines": [10, 5, 6, 7, 8, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 30, 30], "dependencies": ["mcx3d_finance.core.config", "typing", "decimal", "logging", "enum", "builtins", "_frozen_importlib", "abc"], "hash": "e2c84e08f4bdb4649d92b65320ed9c90a9d4a9ce", "id": "mcx3d_finance.core.data_processors", "ignore_all": true, "interface_hash": "6c454ff2675858865310bae768e313c85f1d8db6", "mtime": 1752779258, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/core/data_processors.py", "plugin_data": null, "size": 22879, "suppressed": [], "version_id": "1.15.0"}