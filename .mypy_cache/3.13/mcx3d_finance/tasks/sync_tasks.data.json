{".class": "MypyFile", "_fullname": "mcx3d_finance.tasks.sync_tasks", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "XeroAuthManager": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.auth.xero_oauth.XeroAuthManager", "kind": "Gdef"}, "XeroDataProcessor": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.data_processors.XeroDataProcessor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.tasks.sync_tasks.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.tasks.sync_tasks.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.tasks.sync_tasks.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.tasks.sync_tasks.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.tasks.sync_tasks.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.tasks.sync_tasks.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "celery_app": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.tasks.celery_app.celery_app", "kind": "Gdef"}, "current_task": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.tasks.sync_tasks.current_task", "name": "current_task", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.tasks.sync_tasks.current_task", "source_any": null, "type_of_any": 3}}}, "generate_financial_report": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["org_id", "report_type", "period"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mcx3d_finance.tasks.sync_tasks.generate_financial_report", "name": "generate_financial_report", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["org_id", "report_type", "period"], "arg_types": ["builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_financial_report", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mcx3d_finance.tasks.sync_tasks.generate_financial_report", "name": "generate_financial_report", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.tasks.celery_app.Celery", "source_any": {".class": "AnyType", "missing_import_name": "mcx3d_finance.tasks.celery_app.Celery", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.tasks.sync_tasks.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "sync_xero_data": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "org_id", "incremental"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "mcx3d_finance.tasks.sync_tasks.sync_xero_data", "name": "sync_xero_data", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "org_id", "incremental"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sync_xero_data", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "mcx3d_finance.tasks.sync_tasks.sync_xero_data", "name": "sync_xero_data", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.tasks.celery_app.Celery", "source_any": {".class": "AnyType", "missing_import_name": "mcx3d_finance.tasks.celery_app.Celery", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/tasks/sync_tasks.py"}