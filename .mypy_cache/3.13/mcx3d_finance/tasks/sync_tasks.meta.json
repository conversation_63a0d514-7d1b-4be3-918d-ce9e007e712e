{"data_mtime": 1752778946, "dep_lines": [6, 7, 8, 9, 10, 1, 1, 1, 5], "dep_prios": [5, 5, 5, 10, 5, 5, 30, 30, 5], "dependencies": ["mcx3d_finance.tasks.celery_app", "mcx3d_finance.core.data_processors", "mcx3d_finance.auth.xero_oauth", "logging", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "ca25dbc7b245d40516a4501ccad340692aa1bca2", "id": "mcx3d_finance.tasks.sync_tasks", "ignore_all": true, "interface_hash": "68f99e8e7b356dd4f333c9e1e52adcefb82d6eee", "mtime": 1752777582, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/tasks/sync_tasks.py", "plugin_data": null, "size": 3331, "suppressed": ["celery"], "version_id": "1.15.0"}