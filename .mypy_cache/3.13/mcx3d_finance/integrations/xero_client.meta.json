{"data_mtime": 1752777840, "dep_lines": [20, 21, 22, 1, 2, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, 6, 9, 4, 7, 8], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 5], "dependencies": ["mcx3d_finance.db.session", "mcx3d_finance.db.models", "mcx3d_finance.core.config", "logging", "typing", "datetime", "builtins", "_frozen_importlib", "abc", "sqlalchemy", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.orm", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.session", "sqlalchemy.sql", "sqlalchemy.sql.base", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "types"], "hash": "c1e1d5eb466d27f6d0eb30be4a7c346a26db18f3", "id": "mcx3d_finance.integrations.xero_client", "ignore_all": true, "interface_hash": "58ec282e2287db78567a83d920bfc5bbe783887b", "mtime": 1752779073, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/integrations/xero_client.py", "plugin_data": null, "size": 23020, "suppressed": ["xero_python.api_client.configuration", "xero_python.api_client.oauth2", "xero_python.models.accounting", "xero_python.api_client", "xero_python.accounting", "xero_python.exceptions"], "version_id": "1.15.0"}