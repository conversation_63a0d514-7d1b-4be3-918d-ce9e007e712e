{".class": "MypyFile", "_fullname": "mcx3d_finance.integrations.xero_client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AccountingApi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.AccountingApi", "name": "AccountingApi", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.AccountingApi", "source_any": null, "type_of_any": 3}}}, "Accounts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.Accounts", "name": "Accounts", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.Accounts", "source_any": null, "type_of_any": 3}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApiClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.ApiClient", "name": "ApiClient", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.ApiClient", "source_any": null, "type_of_any": 3}}}, "ApiException": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.ApiException", "name": "ApiException", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.ApiException", "source_any": null, "type_of_any": 3}}}, "BankTransactions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.BankTransactions", "name": "BankTransactions", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.BankTransactions", "source_any": null, "type_of_any": 3}}}, "Bills": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.Bills", "name": "Bills", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.Bills", "source_any": null, "type_of_any": 3}}}, "Configuration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.Configuration", "name": "Configuration", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.Configuration", "source_any": null, "type_of_any": 3}}}, "Contacts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.Contacts", "name": "Contacts", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.Contacts", "source_any": null, "type_of_any": 3}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Invoices": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.Invoices", "name": "Invoices", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.Invoices", "source_any": null, "type_of_any": 3}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "OAuth2Token": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.OAuth2Token", "name": "OAuth2Token", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.OAuth2Token", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Organization": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.db.models.Organization", "kind": "Gdef"}, "ProfitAndLossResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.ProfitAndLossResponse", "name": "ProfitAndLossResponse", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.ProfitAndLossResponse", "source_any": null, "type_of_any": 3}}}, "ReportWithRows": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.ReportWithRows", "name": "ReportWithRows", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.ReportWithRows", "source_any": null, "type_of_any": 3}}}, "SessionLocal": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.db.session.SessionLocal", "kind": "Gdef"}, "TrialBalanceResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.TrialBalanceResponse", "name": "TrialBalanceResponse", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.TrialBalanceResponse", "source_any": null, "type_of_any": 3}}}, "XeroClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mcx3d_finance.integrations.xero_client.XeroClient", "name": "XeroClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mcx3d_finance.integrations.xero_client", "mro": ["mcx3d_finance.integrations.xero_client.XeroClient", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "arg_types": ["mcx3d_finance.integrations.xero_client.XeroClient", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of XeroClient", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_api_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient._create_api_client", "name": "_create_api_client", "type": null}}, "_get_organization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient._get_organization", "name": "_get_organization", "type": null}}, "_token_refreshed_cb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient._token_refreshed_cb", "name": "_token_refreshed_cb", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "new_token"], "arg_types": ["mcx3d_finance.integrations.xero_client.XeroClient", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_token_refreshed_cb of XeroClient", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accounting_api": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.accounting_api", "name": "accounting_api", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.AccountingApi", "source_any": {".class": "AnyType", "missing_import_name": "mcx3d_finance.integrations.xero_client.AccountingApi", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "api_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.api_client", "name": "api_client", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_bank_transactions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.get_bank_transactions", "name": "get_bank_transactions", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "from_date", "to_date"], "arg_types": ["mcx3d_finance.integrations.xero_client.XeroClient", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bank_transactions of XeroClient", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_chart_of_accounts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.get_chart_of_accounts", "name": "get_chart_of_accounts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mcx3d_finance.integrations.xero_client.XeroClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_chart_of_accounts of XeroClient", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_contacts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.get_contacts", "name": "get_contacts", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mcx3d_finance.integrations.xero_client.XeroClient"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_contacts of XeroClient", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_invoices": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.get_invoices", "name": "get_invoices", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "from_date", "to_date"], "arg_types": ["mcx3d_finance.integrations.xero_client.XeroClient", {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_invoices of XeroClient", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_profit_and_loss": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_date", "to_date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.get_profit_and_loss", "name": "get_profit_and_loss", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "from_date", "to_date"], "arg_types": ["mcx3d_finance.integrations.xero_client.XeroClient", "datetime.datetime", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_profit_and_loss of XeroClient", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_trial_balance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "date"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.get_trial_balance", "name": "get_trial_balance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "date"], "arg_types": ["mcx3d_finance.integrations.xero_client.XeroClient", "datetime.datetime"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_trial_balance of XeroClient", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "organization": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.organization", "name": "organization", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "organization_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.organization_id", "name": "organization_id", "type": "builtins.int"}}, "tenant_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.tenant_id", "name": "tenant_id", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mcx3d_finance.integrations.xero_client.XeroClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mcx3d_finance.integrations.xero_client.XeroClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.integrations.xero_client.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.integrations.xero_client.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.integrations.xero_client.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.integrations.xero_client.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.integrations.xero_client.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.integrations.xero_client.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "get_xero_config": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.config.get_xero_config", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.integrations.xero_client.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/integrations/xero_client.py"}