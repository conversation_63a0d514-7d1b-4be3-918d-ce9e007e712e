{"data_mtime": 1752779395, "dep_lines": [5, 6, 1, 1, 1, 1, 1, 1, 10, 19, 20, 21, 9, 11, 25, 24], "dep_prios": [10, 5, 5, 30, 30, 30, 30, 30, 5, 5, 5, 5, 5, 5, 5, 5], "dependencies": ["logging", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "70d4e47b8cd70f1d60dffd0791b7a36804628712", "id": "mcx3d_finance.reporting.generator", "ignore_all": true, "interface_hash": "740596e9a6ed2204eb39c2f2f52bcc072ffa51f0", "mtime": 1752779239, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/reporting/generator.py", "plugin_data": null, "size": 30043, "suppressed": ["reportlab.lib.pagesizes", "reportlab.lib.styles", "reportlab.lib.units", "reportlab.lib.enums", "reportlab.lib", "reportlab.platypus", "openpyxl.styles", "openpyxl"], "version_id": "1.15.0"}