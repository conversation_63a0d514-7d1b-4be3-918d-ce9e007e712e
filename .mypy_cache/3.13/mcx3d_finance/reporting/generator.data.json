{".class": "MypyFile", "_fullname": "mcx3d_finance.reporting.generator", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Alignment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.Alignment", "name": "Alignment", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.Alignment", "source_any": null, "type_of_any": 3}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Font": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.Font", "name": "Font", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.Font", "source_any": null, "type_of_any": 3}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "PageBreak": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.PageBreak", "name": "PageBreak", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.PageBreak", "source_any": null, "type_of_any": 3}}}, "Paragraph": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.Paragraph", "name": "Paragraph", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.Paragraph", "source_any": null, "type_of_any": 3}}}, "ParagraphStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.ParagraphStyle", "name": "ParagraphStyle", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.ParagraphStyle", "source_any": null, "type_of_any": 3}}}, "ReportGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mcx3d_finance.reporting.generator.ReportGenerator", "name": "ReportGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mcx3d_finance.reporting.generator", "mro": ["mcx3d_finance.reporting.generator.ReportGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator.__init__", "name": "__init__", "type": null}}, "_build_balance_sheet_assets_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "assets_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator._build_balance_sheet_assets_table", "name": "_build_balance_sheet_assets_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "assets_data"], "arg_types": ["mcx3d_finance.reporting.generator.ReportGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_balance_sheet_assets_table of ReportGenerator", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_balance_sheet_liabilities_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "liab_equity_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator._build_balance_sheet_liabilities_table", "name": "_build_balance_sheet_liabilities_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "liab_equity_data"], "arg_types": ["mcx3d_finance.reporting.generator.ReportGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_balance_sheet_liabilities_table of ReportGenerator", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_financial_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator._create_financial_table", "name": "_create_financial_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table_data"], "arg_types": ["mcx3d_finance.reporting.generator.ReportGenerator", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_financial_table of ReportGenerator", "ret_type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.Table", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_currency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator._format_currency", "name": "_format_currency", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "amount"], "arg_types": ["mcx3d_finance.reporting.generator.ReportGenerator", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_currency of ReportGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setup_custom_styles": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator._setup_custom_styles", "name": "_setup_custom_styles", "type": null}}, "generate_balance_sheet_excel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "balance_sheet_data", "output_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator.generate_balance_sheet_excel", "name": "generate_balance_sheet_excel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "balance_sheet_data", "output_path"], "arg_types": ["mcx3d_finance.reporting.generator.ReportGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_balance_sheet_excel of ReportGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_balance_sheet_pdf": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "balance_sheet_data", "output_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator.generate_balance_sheet_pdf", "name": "generate_balance_sheet_pdf", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "balance_sheet_data", "output_path"], "arg_types": ["mcx3d_finance.reporting.generator.ReportGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_balance_sheet_pdf of ReportGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_income_statement_excel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "income_statement_data", "output_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator.generate_income_statement_excel", "name": "generate_income_statement_excel", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "income_statement_data", "output_path"], "arg_types": ["mcx3d_finance.reporting.generator.ReportGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_income_statement_excel of ReportGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_income_statement_pdf": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "income_statement_data", "output_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator.generate_income_statement_pdf", "name": "generate_income_statement_pdf", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "income_statement_data", "output_path"], "arg_types": ["mcx3d_finance.reporting.generator.ReportGenerator", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_income_statement_pdf of ReportGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "styles": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.reporting.generator.ReportGenerator.styles", "name": "styles", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mcx3d_finance.reporting.generator.ReportGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mcx3d_finance.reporting.generator.ReportGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SimpleDocTemplate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.SimpleDocTemplate", "name": "SimpleDocTemplate", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.SimpleDocTemplate", "source_any": null, "type_of_any": 3}}}, "Spacer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.Spacer", "name": "Spacer", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.Spacer", "source_any": null, "type_of_any": 3}}}, "TA_CENTER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.TA_CENTER", "name": "TA_CENTER", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.TA_CENTER", "source_any": null, "type_of_any": 3}}}, "TA_RIGHT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.TA_RIGHT", "name": "TA_RIGHT", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.TA_RIGHT", "source_any": null, "type_of_any": 3}}}, "Table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.Table", "name": "Table", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.Table", "source_any": null, "type_of_any": 3}}}, "TableStyle": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.TableStyle", "name": "TableStyle", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.TableStyle", "source_any": null, "type_of_any": 3}}}, "Workbook": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.Workbook", "name": "Workbook", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.Workbook", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.reporting.generator.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.reporting.generator.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.reporting.generator.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.reporting.generator.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.reporting.generator.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.reporting.generator.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "colors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.colors", "name": "colors", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.colors", "source_any": null, "type_of_any": 3}}}, "getSampleStyleSheet": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.getSampleStyleSheet", "name": "getSampleStyleSheet", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.getSampleStyleSheet", "source_any": null, "type_of_any": 3}}}, "inch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.inch", "name": "inch", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.inch", "source_any": null, "type_of_any": 3}}}, "letter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.reporting.generator.letter", "name": "letter", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.reporting.generator.letter", "source_any": null, "type_of_any": 3}}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.reporting.generator.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/reporting/generator.py"}