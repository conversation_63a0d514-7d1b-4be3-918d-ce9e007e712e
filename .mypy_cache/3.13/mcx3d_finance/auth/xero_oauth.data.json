{".class": "MypyFile", "_fullname": "mcx3d_finance.auth.xero_oauth", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ApiClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.auth.xero_oauth.ApiClient", "name": "ApiClient", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.auth.xero_oauth.ApiClient", "source_any": null, "type_of_any": 3}}}, "Configuration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.auth.xero_oauth.Configuration", "name": "Configuration", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.auth.xero_oauth.Configuration", "source_any": null, "type_of_any": 3}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "IdentityApi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.auth.xero_oauth.IdentityApi", "name": "IdentityApi", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.auth.xero_oauth.IdentityApi", "source_any": null, "type_of_any": 3}}}, "OAuth2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.auth.xero_oauth.OAuth2", "name": "OAuth2", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.auth.xero_oauth.OAuth2", "source_any": null, "type_of_any": 3}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Organization": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.db.models.Organization", "kind": "Gdef"}, "SessionLocal": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.db.session.SessionLocal", "kind": "Gdef"}, "UnsuccessfulRefresh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.auth.xero_oauth.UnsuccessfulRefresh", "name": "UnsuccessfulRefresh", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.auth.xero_oauth.UnsuccessfulRefresh", "source_any": null, "type_of_any": 3}}}, "UnsupportedGrantType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "mcx3d_finance.auth.xero_oauth.UnsupportedGrantType", "name": "UnsupportedGrantType", "type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.auth.xero_oauth.UnsupportedGrantType", "source_any": null, "type_of_any": 3}}}, "XeroAuthManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager", "name": "XeroAuthManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "mcx3d_finance.auth.xero_oauth", "mro": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager.__init__", "name": "__init__", "type": null}}, "_create_api_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager._create_api_client", "name": "_create_api_client", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_api_client of XeroAuthManager", "ret_type": {".class": "AnyType", "missing_import_name": "mcx3d_finance.auth.xero_oauth.ApiClient", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_decrypt_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "encrypted_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager._decrypt_token", "name": "_decrypt_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "encrypted_token"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_decrypt_token of XeroAuthManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_encrypt_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager._encrypt_token", "name": "_encrypt_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_encrypt_token of XeroAuthManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_tenant_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "token"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager._get_tenant_info", "name": "_get_tenant_info", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "token"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_tenant_info of XeroAuthManager", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_refresh_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "token", "organization", "db"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager._refresh_token", "name": "_refresh_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "token", "organization", "db"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "mcx3d_finance.db.models.Organization", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_refresh_token of XeroAuthManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_store_organization_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "token", "tenant_info", "db"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager._store_organization_token", "name": "_store_organization_token", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "token", "tenant_info", "db"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_store_organization_token of XeroAuthManager", "ret_type": "mcx3d_finance.db.models.Organization", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_token_needs_refresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "token", "expires_at"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager._token_needs_refresh", "name": "_token_needs_refresh", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "token", "expires_at"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_token_needs_refresh of XeroAuthManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "api_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager.api_client", "name": "api_client", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "generate_auth_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager.generate_auth_url", "name": "generate_auth_url", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "state"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_auth_url of XeroAuthManager", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_valid_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager.get_valid_token", "name": "get_valid_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_valid_token of XeroAuthManager", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_callback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "authorization_response_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager.handle_callback", "name": "handle_callback", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "authorization_response_url"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_callback of XeroAuthManager", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "redis_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager.redis_client", "name": "redis_client", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "revoke_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager.revoke_token", "name": "revoke_token", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "organization_id"], "arg_types": ["mcx3d_finance.auth.xero_oauth.XeroAuthManager", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "revoke_token of XeroAuthManager", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "mcx3d_finance.auth.xero_oauth.XeroAuthManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "mcx3d_finance.auth.xero_oauth.XeroAuthManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.auth.xero_oauth.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.auth.xero_oauth.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.auth.xero_oauth.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.auth.xero_oauth.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.auth.xero_oauth.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "mcx3d_finance.auth.xero_oauth.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "generate_auth_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.generate_auth_url", "name": "generate_auth_url", "type": null}}, "get_redis_client": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.utils.redis_client.get_redis_client", "kind": "Gdef"}, "get_xero_api_client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.get_xero_api_client", "name": "get_xero_api_client", "type": null}}, "get_xero_config": {".class": "SymbolTableNode", "cross_ref": "mcx3d_finance.core.config.get_xero_config", "kind": "Gdef"}, "handle_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["authorization_response_url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "mcx3d_finance.auth.xero_oauth.handle_callback", "name": "handle_callback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["authorization_response_url"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "handle_callback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "mcx3d_finance.auth.xero_oauth.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "parse_qs": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.parse_qs", "kind": "Gdef"}, "secrets": {".class": "SymbolTableNode", "cross_ref": "secrets", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "urlparse": {".class": "SymbolTableNode", "cross_ref": "urllib.parse.urlparse", "kind": "Gdef"}}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/mcx3d_finance/auth/xero_oauth.py"}