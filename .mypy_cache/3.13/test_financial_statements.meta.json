{"data_mtime": 1752778503, "dep_lines": [7, 8, 9, 5, 6, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["mcx3d_finance.core.financials.balance_sheet", "mcx3d_finance.core.financials.income_statement", "mcx3d_finance.core.financials.cash_flow", "pytest", "datetime", "builtins", "_frozen_importlib", "abc", "mcx3d_finance", "mcx3d_finance.core", "mcx3d_finance.core.financials", "typing"], "hash": "9c95f14af2d6551686a94e36b6d4ad942ab032dd", "id": "test_financial_statements", "ignore_all": false, "interface_hash": "2ddb63444d2efc71fcbea7139aaa65691027c64e", "mtime": 1752778504, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/tests/test_financial_statements.py", "plugin_data": null, "size": 1597, "suppressed": [], "version_id": "1.15.0"}