{"data_mtime": 1752781219, "dep_lines": [1, 2, 3, 4, 6, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["sys", "types", "pathlib", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "os"], "hash": "1f25fafd8ff46501e763f6553c6d1293ae12c3dc", "id": "pydantic_settings.utils", "ignore_all": true, "interface_hash": "8918f44d7df8f64d0bddecd8c25a29e94fc94db7", "mtime": 1752781094, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "darwin", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "/Users/<USER>/Documents/GitHub/mcx3d_financials/v2/venv/lib/python3.13/site-packages/pydantic_settings/utils.py", "plugin_data": null, "size": 1382, "suppressed": [], "version_id": "1.15.0"}