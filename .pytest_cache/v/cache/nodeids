["tests/core/financials/test_income_statement.py::test_calculate_income_statement_with_missing_account_types", "tests/core/financials/test_income_statement.py::test_calculate_income_statement_with_no_transactions", "tests/core/financials/test_income_statement.py::test_calculate_income_statement_with_sample_data", "tests/core/financials/test_income_statement.py::test_calculate_income_statement_with_zero_amounts", "tests/core/metrics/test_saas_kpis.py::test_calculate_arr", "tests/core/metrics/test_saas_kpis.py::test_calculate_cac", "tests/core/metrics/test_saas_kpis.py::test_calculate_ltv", "tests/core/metrics/test_saas_kpis.py::test_calculate_mrr_with_empty_data", "tests/core/metrics/test_saas_kpis.py::test_calculate_mrr_with_missing_columns", "tests/core/metrics/test_saas_kpis.py::test_calculate_mrr_with_sample_data", "tests/core/metrics/test_saas_kpis.py::test_rule_of_40", "tests/core/valuation/test_dcf.py::test_calculate_dcf_valuation_with_equal_rates", "tests/core/valuation/test_dcf.py::test_calculate_dcf_valuation_with_no_projections", "tests/core/valuation/test_dcf.py::test_calculate_dcf_valuation_with_sample_data", "tests/core/valuation/test_dcf.py::test_calculate_dcf_valuation_with_zero_discount_rate", "tests/e2e/test_api.py::test_get_income_statement_invalid_params", "tests/e2e/test_api.py::test_get_income_statement_success", "tests/e2e/test_cli.py::test_generate_income_statement_missing_option", "tests/e2e/test_cli.py::test_generate_income_statement_success", "tests/integration/test_data_pipeline.py::TestDataPipeline::test_full_data_sync_and_report_generation", "tests/integration/test_data_pipeline.py::TestDataPipeline::test_webhook_processing"]