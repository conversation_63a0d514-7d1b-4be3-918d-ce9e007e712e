# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class Journal(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "journal_id": "str",
        "journal_date": "date[ms-format]",
        "journal_number": "int",
        "created_date_utc": "datetime[ms-format]",
        "reference": "str",
        "source_id": "str",
        "source_type": "str",
        "journal_lines": "list[JournalLine]",
    }

    attribute_map = {
        "journal_id": "JournalID",
        "journal_date": "JournalDate",
        "journal_number": "JournalNumber",
        "created_date_utc": "CreatedDateUTC",
        "reference": "Reference",
        "source_id": "SourceID",
        "source_type": "SourceType",
        "journal_lines": "JournalLines",
    }

    def __init__(
        self,
        journal_id=None,
        journal_date=None,
        journal_number=None,
        created_date_utc=None,
        reference=None,
        source_id=None,
        source_type=None,
        journal_lines=None,
    ):  # noqa: E501
        """Journal - a model defined in OpenAPI"""  # noqa: E501

        self._journal_id = None
        self._journal_date = None
        self._journal_number = None
        self._created_date_utc = None
        self._reference = None
        self._source_id = None
        self._source_type = None
        self._journal_lines = None
        self.discriminator = None

        if journal_id is not None:
            self.journal_id = journal_id
        if journal_date is not None:
            self.journal_date = journal_date
        if journal_number is not None:
            self.journal_number = journal_number
        if created_date_utc is not None:
            self.created_date_utc = created_date_utc
        if reference is not None:
            self.reference = reference
        if source_id is not None:
            self.source_id = source_id
        if source_type is not None:
            self.source_type = source_type
        if journal_lines is not None:
            self.journal_lines = journal_lines

    @property
    def journal_id(self):
        """Gets the journal_id of this Journal.  # noqa: E501

        Xero identifier  # noqa: E501

        :return: The journal_id of this Journal.  # noqa: E501
        :rtype: str
        """
        return self._journal_id

    @journal_id.setter
    def journal_id(self, journal_id):
        """Sets the journal_id of this Journal.

        Xero identifier  # noqa: E501

        :param journal_id: The journal_id of this Journal.  # noqa: E501
        :type: str
        """

        self._journal_id = journal_id

    @property
    def journal_date(self):
        """Gets the journal_date of this Journal.  # noqa: E501

        Date the journal was posted  # noqa: E501

        :return: The journal_date of this Journal.  # noqa: E501
        :rtype: date
        """
        return self._journal_date

    @journal_date.setter
    def journal_date(self, journal_date):
        """Sets the journal_date of this Journal.

        Date the journal was posted  # noqa: E501

        :param journal_date: The journal_date of this Journal.  # noqa: E501
        :type: date
        """

        self._journal_date = journal_date

    @property
    def journal_number(self):
        """Gets the journal_number of this Journal.  # noqa: E501

        Xero generated journal number  # noqa: E501

        :return: The journal_number of this Journal.  # noqa: E501
        :rtype: int
        """
        return self._journal_number

    @journal_number.setter
    def journal_number(self, journal_number):
        """Sets the journal_number of this Journal.

        Xero generated journal number  # noqa: E501

        :param journal_number: The journal_number of this Journal.  # noqa: E501
        :type: int
        """

        self._journal_number = journal_number

    @property
    def created_date_utc(self):
        """Gets the created_date_utc of this Journal.  # noqa: E501

        Created date UTC format  # noqa: E501

        :return: The created_date_utc of this Journal.  # noqa: E501
        :rtype: datetime
        """
        return self._created_date_utc

    @created_date_utc.setter
    def created_date_utc(self, created_date_utc):
        """Sets the created_date_utc of this Journal.

        Created date UTC format  # noqa: E501

        :param created_date_utc: The created_date_utc of this Journal.  # noqa: E501
        :type: datetime
        """

        self._created_date_utc = created_date_utc

    @property
    def reference(self):
        """Gets the reference of this Journal.  # noqa: E501

        reference field for additional indetifying information  # noqa: E501

        :return: The reference of this Journal.  # noqa: E501
        :rtype: str
        """
        return self._reference

    @reference.setter
    def reference(self, reference):
        """Sets the reference of this Journal.

        reference field for additional indetifying information  # noqa: E501

        :param reference: The reference of this Journal.  # noqa: E501
        :type: str
        """

        self._reference = reference

    @property
    def source_id(self):
        """Gets the source_id of this Journal.  # noqa: E501

        The identifier for the source transaction (e.g. InvoiceID)  # noqa: E501

        :return: The source_id of this Journal.  # noqa: E501
        :rtype: str
        """
        return self._source_id

    @source_id.setter
    def source_id(self, source_id):
        """Sets the source_id of this Journal.

        The identifier for the source transaction (e.g. InvoiceID)  # noqa: E501

        :param source_id: The source_id of this Journal.  # noqa: E501
        :type: str
        """

        self._source_id = source_id

    @property
    def source_type(self):
        """Gets the source_type of this Journal.  # noqa: E501

        The journal source type. The type of transaction that created the journal  # noqa: E501

        :return: The source_type of this Journal.  # noqa: E501
        :rtype: str
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this Journal.

        The journal source type. The type of transaction that created the journal  # noqa: E501

        :param source_type: The source_type of this Journal.  # noqa: E501
        :type: str
        """
        allowed_values = [
            "ACCREC",
            "ACCPAY",
            "ACCRECCREDIT",
            "ACCPAYCREDIT",
            "ACCRECPAYMENT",
            "ACCPAYPAYMENT",
            "ARCREDITPAYMENT",
            "APCREDITPAYMENT",
            "CASHREC",
            "CASHPAID",
            "TRANSFER",
            "ARPREPAYMENT",
            "APPREPAYMENT",
            "AROVERPAYMENT",
            "APOVERPAYMENT",
            "EXPCLAIM",
            "EXPPAYMENT",
            "MANJOURNAL",
            "PAYSLIP",
            "WAGEPAYABLE",
            "INTEGRATEDPAYROLLPE",
            "INTEGRATEDPAYROLLPT",
            "EXTERNALSPENDMONEY",
            "INTEGRATEDPAYROLLPTPAYMENT",
            "INTEGRATEDPAYROLLCN",
            "None",
        ]  # noqa: E501

        if source_type:
            if source_type not in allowed_values:
                raise ValueError(
                    "Invalid value for `source_type` ({0}), must be one of {1}".format(  # noqa: E501
                        source_type, allowed_values
                    )
                )

        self._source_type = source_type

    @property
    def journal_lines(self):
        """Gets the journal_lines of this Journal.  # noqa: E501

        See JournalLines  # noqa: E501

        :return: The journal_lines of this Journal.  # noqa: E501
        :rtype: list[JournalLine]
        """
        return self._journal_lines

    @journal_lines.setter
    def journal_lines(self, journal_lines):
        """Sets the journal_lines of this Journal.

        See JournalLines  # noqa: E501

        :param journal_lines: The journal_lines of this Journal.  # noqa: E501
        :type: list[JournalLine]
        """

        self._journal_lines = journal_lines
