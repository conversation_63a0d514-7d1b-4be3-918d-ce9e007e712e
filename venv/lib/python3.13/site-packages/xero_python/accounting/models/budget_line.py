# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class BudgetLine(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "account_id": "str",
        "account_code": "str",
        "budget_balances": "list[BudgetBalance]",
    }

    attribute_map = {
        "account_id": "AccountID",
        "account_code": "AccountCode",
        "budget_balances": "BudgetBalances",
    }

    def __init__(
        self, account_id=None, account_code=None, budget_balances=None
    ):  # noqa: E501
        """BudgetLine - a model defined in OpenAPI"""  # noqa: E501

        self._account_id = None
        self._account_code = None
        self._budget_balances = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if account_code is not None:
            self.account_code = account_code
        if budget_balances is not None:
            self.budget_balances = budget_balances

    @property
    def account_id(self):
        """Gets the account_id of this BudgetLine.  # noqa: E501

        See Accounts  # noqa: E501

        :return: The account_id of this BudgetLine.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this BudgetLine.

        See Accounts  # noqa: E501

        :param account_id: The account_id of this BudgetLine.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def account_code(self):
        """Gets the account_code of this BudgetLine.  # noqa: E501

        See Accounts  # noqa: E501

        :return: The account_code of this BudgetLine.  # noqa: E501
        :rtype: str
        """
        return self._account_code

    @account_code.setter
    def account_code(self, account_code):
        """Sets the account_code of this BudgetLine.

        See Accounts  # noqa: E501

        :param account_code: The account_code of this BudgetLine.  # noqa: E501
        :type: str
        """

        self._account_code = account_code

    @property
    def budget_balances(self):
        """Gets the budget_balances of this BudgetLine.  # noqa: E501


        :return: The budget_balances of this BudgetLine.  # noqa: E501
        :rtype: list[BudgetBalance]
        """
        return self._budget_balances

    @budget_balances.setter
    def budget_balances(self, budget_balances):
        """Sets the budget_balances of this BudgetLine.


        :param budget_balances: The budget_balances of this BudgetLine.  # noqa: E501
        :type: list[BudgetBalance]
        """

        self._budget_balances = budget_balances
