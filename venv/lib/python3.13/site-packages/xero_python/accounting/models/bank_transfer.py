# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class BankTransfer(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "from_bank_account": "Account",
        "to_bank_account": "Account",
        "amount": "float",
        "date": "date[ms-format]",
        "bank_transfer_id": "str",
        "currency_rate": "float",
        "from_bank_transaction_id": "str",
        "to_bank_transaction_id": "str",
        "from_is_reconciled": "bool",
        "to_is_reconciled": "bool",
        "reference": "str",
        "has_attachments": "bool",
        "created_date_utc": "datetime[ms-format]",
        "validation_errors": "list[ValidationError]",
    }

    attribute_map = {
        "from_bank_account": "FromBankAccount",
        "to_bank_account": "ToBankAccount",
        "amount": "Amount",
        "date": "Date",
        "bank_transfer_id": "BankTransferID",
        "currency_rate": "CurrencyRate",
        "from_bank_transaction_id": "FromBankTransactionID",
        "to_bank_transaction_id": "ToBankTransactionID",
        "from_is_reconciled": "FromIsReconciled",
        "to_is_reconciled": "ToIsReconciled",
        "reference": "Reference",
        "has_attachments": "HasAttachments",
        "created_date_utc": "CreatedDateUTC",
        "validation_errors": "ValidationErrors",
    }

    def __init__(
        self,
        from_bank_account=None,
        to_bank_account=None,
        amount=None,
        date=None,
        bank_transfer_id=None,
        currency_rate=None,
        from_bank_transaction_id=None,
        to_bank_transaction_id=None,
        from_is_reconciled=False,
        to_is_reconciled=False,
        reference=None,
        has_attachments=False,
        created_date_utc=None,
        validation_errors=None,
    ):  # noqa: E501
        """BankTransfer - a model defined in OpenAPI"""  # noqa: E501

        self._from_bank_account = None
        self._to_bank_account = None
        self._amount = None
        self._date = None
        self._bank_transfer_id = None
        self._currency_rate = None
        self._from_bank_transaction_id = None
        self._to_bank_transaction_id = None
        self._from_is_reconciled = None
        self._to_is_reconciled = None
        self._reference = None
        self._has_attachments = None
        self._created_date_utc = None
        self._validation_errors = None
        self.discriminator = None

        self.from_bank_account = from_bank_account
        self.to_bank_account = to_bank_account
        self.amount = amount
        if date is not None:
            self.date = date
        if bank_transfer_id is not None:
            self.bank_transfer_id = bank_transfer_id
        if currency_rate is not None:
            self.currency_rate = currency_rate
        if from_bank_transaction_id is not None:
            self.from_bank_transaction_id = from_bank_transaction_id
        if to_bank_transaction_id is not None:
            self.to_bank_transaction_id = to_bank_transaction_id
        if from_is_reconciled is not None:
            self.from_is_reconciled = from_is_reconciled
        if to_is_reconciled is not None:
            self.to_is_reconciled = to_is_reconciled
        if reference is not None:
            self.reference = reference
        if has_attachments is not None:
            self.has_attachments = has_attachments
        if created_date_utc is not None:
            self.created_date_utc = created_date_utc
        if validation_errors is not None:
            self.validation_errors = validation_errors

    @property
    def from_bank_account(self):
        """Gets the from_bank_account of this BankTransfer.  # noqa: E501


        :return: The from_bank_account of this BankTransfer.  # noqa: E501
        :rtype: Account
        """
        return self._from_bank_account

    @from_bank_account.setter
    def from_bank_account(self, from_bank_account):
        """Sets the from_bank_account of this BankTransfer.


        :param from_bank_account: The from_bank_account of this BankTransfer.  # noqa: E501
        :type: Account
        """
        if from_bank_account is None:
            raise ValueError(
                "Invalid value for `from_bank_account`, must not be `None`"
            )  # noqa: E501

        self._from_bank_account = from_bank_account

    @property
    def to_bank_account(self):
        """Gets the to_bank_account of this BankTransfer.  # noqa: E501


        :return: The to_bank_account of this BankTransfer.  # noqa: E501
        :rtype: Account
        """
        return self._to_bank_account

    @to_bank_account.setter
    def to_bank_account(self, to_bank_account):
        """Sets the to_bank_account of this BankTransfer.


        :param to_bank_account: The to_bank_account of this BankTransfer.  # noqa: E501
        :type: Account
        """
        if to_bank_account is None:
            raise ValueError(
                "Invalid value for `to_bank_account`, must not be `None`"
            )  # noqa: E501

        self._to_bank_account = to_bank_account

    @property
    def amount(self):
        """Gets the amount of this BankTransfer.  # noqa: E501

        amount of the transaction  # noqa: E501

        :return: The amount of this BankTransfer.  # noqa: E501
        :rtype: float
        """
        return self._amount

    @amount.setter
    def amount(self, amount):
        """Sets the amount of this BankTransfer.

        amount of the transaction  # noqa: E501

        :param amount: The amount of this BankTransfer.  # noqa: E501
        :type: float
        """
        if amount is None:
            raise ValueError(
                "Invalid value for `amount`, must not be `None`"
            )  # noqa: E501

        self._amount = amount

    @property
    def date(self):
        """Gets the date of this BankTransfer.  # noqa: E501

        The date of the Transfer YYYY-MM-DD  # noqa: E501

        :return: The date of this BankTransfer.  # noqa: E501
        :rtype: date
        """
        return self._date

    @date.setter
    def date(self, date):
        """Sets the date of this BankTransfer.

        The date of the Transfer YYYY-MM-DD  # noqa: E501

        :param date: The date of this BankTransfer.  # noqa: E501
        :type: date
        """

        self._date = date

    @property
    def bank_transfer_id(self):
        """Gets the bank_transfer_id of this BankTransfer.  # noqa: E501

        The identifier of the Bank Transfer  # noqa: E501

        :return: The bank_transfer_id of this BankTransfer.  # noqa: E501
        :rtype: str
        """
        return self._bank_transfer_id

    @bank_transfer_id.setter
    def bank_transfer_id(self, bank_transfer_id):
        """Sets the bank_transfer_id of this BankTransfer.

        The identifier of the Bank Transfer  # noqa: E501

        :param bank_transfer_id: The bank_transfer_id of this BankTransfer.  # noqa: E501
        :type: str
        """

        self._bank_transfer_id = bank_transfer_id

    @property
    def currency_rate(self):
        """Gets the currency_rate of this BankTransfer.  # noqa: E501

        The currency rate  # noqa: E501

        :return: The currency_rate of this BankTransfer.  # noqa: E501
        :rtype: float
        """
        return self._currency_rate

    @currency_rate.setter
    def currency_rate(self, currency_rate):
        """Sets the currency_rate of this BankTransfer.

        The currency rate  # noqa: E501

        :param currency_rate: The currency_rate of this BankTransfer.  # noqa: E501
        :type: float
        """

        self._currency_rate = currency_rate

    @property
    def from_bank_transaction_id(self):
        """Gets the from_bank_transaction_id of this BankTransfer.  # noqa: E501

        The Bank Transaction ID for the source account  # noqa: E501

        :return: The from_bank_transaction_id of this BankTransfer.  # noqa: E501
        :rtype: str
        """
        return self._from_bank_transaction_id

    @from_bank_transaction_id.setter
    def from_bank_transaction_id(self, from_bank_transaction_id):
        """Sets the from_bank_transaction_id of this BankTransfer.

        The Bank Transaction ID for the source account  # noqa: E501

        :param from_bank_transaction_id: The from_bank_transaction_id of this BankTransfer.  # noqa: E501
        :type: str
        """

        self._from_bank_transaction_id = from_bank_transaction_id

    @property
    def to_bank_transaction_id(self):
        """Gets the to_bank_transaction_id of this BankTransfer.  # noqa: E501

        The Bank Transaction ID for the destination account  # noqa: E501

        :return: The to_bank_transaction_id of this BankTransfer.  # noqa: E501
        :rtype: str
        """
        return self._to_bank_transaction_id

    @to_bank_transaction_id.setter
    def to_bank_transaction_id(self, to_bank_transaction_id):
        """Sets the to_bank_transaction_id of this BankTransfer.

        The Bank Transaction ID for the destination account  # noqa: E501

        :param to_bank_transaction_id: The to_bank_transaction_id of this BankTransfer.  # noqa: E501
        :type: str
        """

        self._to_bank_transaction_id = to_bank_transaction_id

    @property
    def from_is_reconciled(self):
        """Gets the from_is_reconciled of this BankTransfer.  # noqa: E501

        The Bank Transaction boolean to show if it is reconciled for the source account  # noqa: E501

        :return: The from_is_reconciled of this BankTransfer.  # noqa: E501
        :rtype: bool
        """
        return self._from_is_reconciled

    @from_is_reconciled.setter
    def from_is_reconciled(self, from_is_reconciled):
        """Sets the from_is_reconciled of this BankTransfer.

        The Bank Transaction boolean to show if it is reconciled for the source account  # noqa: E501

        :param from_is_reconciled: The from_is_reconciled of this BankTransfer.  # noqa: E501
        :type: bool
        """

        self._from_is_reconciled = from_is_reconciled

    @property
    def to_is_reconciled(self):
        """Gets the to_is_reconciled of this BankTransfer.  # noqa: E501

        The Bank Transaction boolean to show if it is reconciled for the destination account  # noqa: E501

        :return: The to_is_reconciled of this BankTransfer.  # noqa: E501
        :rtype: bool
        """
        return self._to_is_reconciled

    @to_is_reconciled.setter
    def to_is_reconciled(self, to_is_reconciled):
        """Sets the to_is_reconciled of this BankTransfer.

        The Bank Transaction boolean to show if it is reconciled for the destination account  # noqa: E501

        :param to_is_reconciled: The to_is_reconciled of this BankTransfer.  # noqa: E501
        :type: bool
        """

        self._to_is_reconciled = to_is_reconciled

    @property
    def reference(self):
        """Gets the reference of this BankTransfer.  # noqa: E501

        Reference for the transactions.  # noqa: E501

        :return: The reference of this BankTransfer.  # noqa: E501
        :rtype: str
        """
        return self._reference

    @reference.setter
    def reference(self, reference):
        """Sets the reference of this BankTransfer.

        Reference for the transactions.  # noqa: E501

        :param reference: The reference of this BankTransfer.  # noqa: E501
        :type: str
        """

        self._reference = reference

    @property
    def has_attachments(self):
        """Gets the has_attachments of this BankTransfer.  # noqa: E501

        Boolean to indicate if a Bank Transfer has an attachment  # noqa: E501

        :return: The has_attachments of this BankTransfer.  # noqa: E501
        :rtype: bool
        """
        return self._has_attachments

    @has_attachments.setter
    def has_attachments(self, has_attachments):
        """Sets the has_attachments of this BankTransfer.

        Boolean to indicate if a Bank Transfer has an attachment  # noqa: E501

        :param has_attachments: The has_attachments of this BankTransfer.  # noqa: E501
        :type: bool
        """

        self._has_attachments = has_attachments

    @property
    def created_date_utc(self):
        """Gets the created_date_utc of this BankTransfer.  # noqa: E501

        UTC timestamp of creation date of bank transfer  # noqa: E501

        :return: The created_date_utc of this BankTransfer.  # noqa: E501
        :rtype: datetime
        """
        return self._created_date_utc

    @created_date_utc.setter
    def created_date_utc(self, created_date_utc):
        """Sets the created_date_utc of this BankTransfer.

        UTC timestamp of creation date of bank transfer  # noqa: E501

        :param created_date_utc: The created_date_utc of this BankTransfer.  # noqa: E501
        :type: datetime
        """

        self._created_date_utc = created_date_utc

    @property
    def validation_errors(self):
        """Gets the validation_errors of this BankTransfer.  # noqa: E501

        Displays array of validation error messages from the API  # noqa: E501

        :return: The validation_errors of this BankTransfer.  # noqa: E501
        :rtype: list[ValidationError]
        """
        return self._validation_errors

    @validation_errors.setter
    def validation_errors(self, validation_errors):
        """Sets the validation_errors of this BankTransfer.

        Displays array of validation error messages from the API  # noqa: E501

        :param validation_errors: The validation_errors of this BankTransfer.  # noqa: E501
        :type: list[ValidationError]
        """

        self._validation_errors = validation_errors
