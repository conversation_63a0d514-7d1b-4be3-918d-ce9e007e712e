# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class Budgets(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {"budgets": "list[Budget]"}

    attribute_map = {"budgets": "Budgets"}

    def __init__(self, budgets=None):  # noqa: E501
        """Budgets - a model defined in OpenAPI"""  # noqa: E501

        self._budgets = None
        self.discriminator = None

        if budgets is not None:
            self.budgets = budgets

    @property
    def budgets(self):
        """Gets the budgets of this Budgets.  # noqa: E501


        :return: The budgets of this Budgets.  # noqa: E501
        :rtype: list[Budget]
        """
        return self._budgets

    @budgets.setter
    def budgets(self, budgets):
        """Sets the budgets of this Budgets.


        :param budgets: The budgets of this Budgets.  # noqa: E501
        :type: list[Budget]
        """

        self._budgets = budgets
