# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class GetCreditNotesResponse(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "id": "str",
        "status": "str",
        "provider_name": "str",
        "date_time_utc": "str",
        "page_info": "PageInfo",
        "credit_notes": "list[CreditNote]",
    }

    attribute_map = {
        "id": "Id",
        "status": "Status",
        "provider_name": "ProviderName",
        "date_time_utc": "DateTimeUTC",
        "page_info": "PageInfo",
        "credit_notes": "CreditNotes",
    }

    def __init__(
        self,
        id=None,
        status=None,
        provider_name=None,
        date_time_utc=None,
        page_info=None,
        credit_notes=None,
    ):  # noqa: E501
        """GetCreditNotesResponse - a model defined in OpenAPI"""  # noqa: E501

        self._id = None
        self._status = None
        self._provider_name = None
        self._date_time_utc = None
        self._page_info = None
        self._credit_notes = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if status is not None:
            self.status = status
        if provider_name is not None:
            self.provider_name = provider_name
        if date_time_utc is not None:
            self.date_time_utc = date_time_utc
        if page_info is not None:
            self.page_info = page_info
        if credit_notes is not None:
            self.credit_notes = credit_notes

    @property
    def id(self):
        """Gets the id of this GetCreditNotesResponse.  # noqa: E501


        :return: The id of this GetCreditNotesResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetCreditNotesResponse.


        :param id: The id of this GetCreditNotesResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def status(self):
        """Gets the status of this GetCreditNotesResponse.  # noqa: E501


        :return: The status of this GetCreditNotesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetCreditNotesResponse.


        :param status: The status of this GetCreditNotesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def provider_name(self):
        """Gets the provider_name of this GetCreditNotesResponse.  # noqa: E501


        :return: The provider_name of this GetCreditNotesResponse.  # noqa: E501
        :rtype: str
        """
        return self._provider_name

    @provider_name.setter
    def provider_name(self, provider_name):
        """Sets the provider_name of this GetCreditNotesResponse.


        :param provider_name: The provider_name of this GetCreditNotesResponse.  # noqa: E501
        :type: str
        """

        self._provider_name = provider_name

    @property
    def date_time_utc(self):
        """Gets the date_time_utc of this GetCreditNotesResponse.  # noqa: E501


        :return: The date_time_utc of this GetCreditNotesResponse.  # noqa: E501
        :rtype: str
        """
        return self._date_time_utc

    @date_time_utc.setter
    def date_time_utc(self, date_time_utc):
        """Sets the date_time_utc of this GetCreditNotesResponse.


        :param date_time_utc: The date_time_utc of this GetCreditNotesResponse.  # noqa: E501
        :type: str
        """

        self._date_time_utc = date_time_utc

    @property
    def page_info(self):
        """Gets the page_info of this GetCreditNotesResponse.  # noqa: E501


        :return: The page_info of this GetCreditNotesResponse.  # noqa: E501
        :rtype: PageInfo
        """
        return self._page_info

    @page_info.setter
    def page_info(self, page_info):
        """Sets the page_info of this GetCreditNotesResponse.


        :param page_info: The page_info of this GetCreditNotesResponse.  # noqa: E501
        :type: PageInfo
        """

        self._page_info = page_info

    @property
    def credit_notes(self):
        """Gets the credit_notes of this GetCreditNotesResponse.  # noqa: E501


        :return: The credit_notes of this GetCreditNotesResponse.  # noqa: E501
        :rtype: list[CreditNote]
        """
        return self._credit_notes

    @credit_notes.setter
    def credit_notes(self, credit_notes):
        """Sets the credit_notes of this GetCreditNotesResponse.


        :param credit_notes: The credit_notes of this GetCreditNotesResponse.  # noqa: E501
        :type: list[CreditNote]
        """

        self._credit_notes = credit_notes
