# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class Allocations(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {"allocations": "list[Allocation]"}

    attribute_map = {"allocations": "Allocations"}

    def __init__(self, allocations=None):  # noqa: E501
        """Allocations - a model defined in OpenAPI"""  # noqa: E501

        self._allocations = None
        self.discriminator = None

        if allocations is not None:
            self.allocations = allocations

    @property
    def allocations(self):
        """Gets the allocations of this Allocations.  # noqa: E501


        :return: The allocations of this Allocations.  # noqa: E501
        :rtype: list[Allocation]
        """
        return self._allocations

    @allocations.setter
    def allocations(self, allocations):
        """Sets the allocations of this Allocations.


        :param allocations: The allocations of this Allocations.  # noqa: E501
        :type: list[Allocation]
        """

        self._allocations = allocations
