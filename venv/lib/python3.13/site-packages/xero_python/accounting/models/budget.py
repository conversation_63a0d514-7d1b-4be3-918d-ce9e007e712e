# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class Budget(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "budget_id": "str",
        "type": "str",
        "description": "str",
        "updated_date_utc": "datetime[ms-format]",
        "budget_lines": "list[BudgetLine]",
        "tracking": "list[TrackingCategory]",
    }

    attribute_map = {
        "budget_id": "BudgetID",
        "type": "Type",
        "description": "Description",
        "updated_date_utc": "UpdatedDateUTC",
        "budget_lines": "BudgetLines",
        "tracking": "Tracking",
    }

    def __init__(
        self,
        budget_id=None,
        type=None,
        description=None,
        updated_date_utc=None,
        budget_lines=None,
        tracking=None,
    ):  # noqa: E501
        """Budget - a model defined in OpenAPI"""  # noqa: E501

        self._budget_id = None
        self._type = None
        self._description = None
        self._updated_date_utc = None
        self._budget_lines = None
        self._tracking = None
        self.discriminator = None

        if budget_id is not None:
            self.budget_id = budget_id
        if type is not None:
            self.type = type
        if description is not None:
            self.description = description
        if updated_date_utc is not None:
            self.updated_date_utc = updated_date_utc
        if budget_lines is not None:
            self.budget_lines = budget_lines
        if tracking is not None:
            self.tracking = tracking

    @property
    def budget_id(self):
        """Gets the budget_id of this Budget.  # noqa: E501

        Xero identifier  # noqa: E501

        :return: The budget_id of this Budget.  # noqa: E501
        :rtype: str
        """
        return self._budget_id

    @budget_id.setter
    def budget_id(self, budget_id):
        """Sets the budget_id of this Budget.

        Xero identifier  # noqa: E501

        :param budget_id: The budget_id of this Budget.  # noqa: E501
        :type: str
        """

        self._budget_id = budget_id

    @property
    def type(self):
        """Gets the type of this Budget.  # noqa: E501

        Type of Budget. OVERALL or TRACKING  # noqa: E501

        :return: The type of this Budget.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this Budget.

        Type of Budget. OVERALL or TRACKING  # noqa: E501

        :param type: The type of this Budget.  # noqa: E501
        :type: str
        """
        allowed_values = ["OVERALL", "TRACKING", "None"]  # noqa: E501

        if type:
            if type not in allowed_values:
                raise ValueError(
                    "Invalid value for `type` ({0}), must be one of {1}".format(  # noqa: E501
                        type, allowed_values
                    )
                )

        self._type = type

    @property
    def description(self):
        """Gets the description of this Budget.  # noqa: E501

        The Budget description  # noqa: E501

        :return: The description of this Budget.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this Budget.

        The Budget description  # noqa: E501

        :param description: The description of this Budget.  # noqa: E501
        :type: str
        """
        if description is not None and len(description) > 255:
            raise ValueError(
                "Invalid value for `description`, "
                "length must be less than or equal to `255`"
            )  # noqa: E501

        self._description = description

    @property
    def updated_date_utc(self):
        """Gets the updated_date_utc of this Budget.  # noqa: E501

        UTC timestamp of last update to budget  # noqa: E501

        :return: The updated_date_utc of this Budget.  # noqa: E501
        :rtype: datetime
        """
        return self._updated_date_utc

    @updated_date_utc.setter
    def updated_date_utc(self, updated_date_utc):
        """Sets the updated_date_utc of this Budget.

        UTC timestamp of last update to budget  # noqa: E501

        :param updated_date_utc: The updated_date_utc of this Budget.  # noqa: E501
        :type: datetime
        """

        self._updated_date_utc = updated_date_utc

    @property
    def budget_lines(self):
        """Gets the budget_lines of this Budget.  # noqa: E501


        :return: The budget_lines of this Budget.  # noqa: E501
        :rtype: list[BudgetLine]
        """
        return self._budget_lines

    @budget_lines.setter
    def budget_lines(self, budget_lines):
        """Sets the budget_lines of this Budget.


        :param budget_lines: The budget_lines of this Budget.  # noqa: E501
        :type: list[BudgetLine]
        """

        self._budget_lines = budget_lines

    @property
    def tracking(self):
        """Gets the tracking of this Budget.  # noqa: E501


        :return: The tracking of this Budget.  # noqa: E501
        :rtype: list[TrackingCategory]
        """
        return self._tracking

    @tracking.setter
    def tracking(self, tracking):
        """Sets the tracking of this Budget.


        :param tracking: The tracking of this Budget.  # noqa: E501
        :type: list[TrackingCategory]
        """

        self._tracking = tracking
