# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class BalanceDetails(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "balance": "float",
        "currency_code": "str",
        "currency_rate": "float",
    }

    attribute_map = {
        "balance": "Balance",
        "currency_code": "CurrencyCode",
        "currency_rate": "CurrencyRate",
    }

    def __init__(
        self, balance=None, currency_code=None, currency_rate=None
    ):  # noqa: E501
        """BalanceDetails - a model defined in OpenAPI"""  # noqa: E501

        self._balance = None
        self._currency_code = None
        self._currency_rate = None
        self.discriminator = None

        if balance is not None:
            self.balance = balance
        if currency_code is not None:
            self.currency_code = currency_code
        if currency_rate is not None:
            self.currency_rate = currency_rate

    @property
    def balance(self):
        """Gets the balance of this BalanceDetails.  # noqa: E501

        The opening balances of the account. Debits are positive, credits are negative values  # noqa: E501

        :return: The balance of this BalanceDetails.  # noqa: E501
        :rtype: float
        """
        return self._balance

    @balance.setter
    def balance(self, balance):
        """Sets the balance of this BalanceDetails.

        The opening balances of the account. Debits are positive, credits are negative values  # noqa: E501

        :param balance: The balance of this BalanceDetails.  # noqa: E501
        :type: float
        """

        self._balance = balance

    @property
    def currency_code(self):
        """Gets the currency_code of this BalanceDetails.  # noqa: E501

        The currency of the balance (Not required for base currency)  # noqa: E501

        :return: The currency_code of this BalanceDetails.  # noqa: E501
        :rtype: str
        """
        return self._currency_code

    @currency_code.setter
    def currency_code(self, currency_code):
        """Sets the currency_code of this BalanceDetails.

        The currency of the balance (Not required for base currency)  # noqa: E501

        :param currency_code: The currency_code of this BalanceDetails.  # noqa: E501
        :type: str
        """

        self._currency_code = currency_code

    @property
    def currency_rate(self):
        """Gets the currency_rate of this BalanceDetails.  # noqa: E501

        (Optional) Exchange rate to base currency when money is spent or received. If not specified, XE rate for the day is applied  # noqa: E501

        :return: The currency_rate of this BalanceDetails.  # noqa: E501
        :rtype: float
        """
        return self._currency_rate

    @currency_rate.setter
    def currency_rate(self, currency_rate):
        """Sets the currency_rate of this BalanceDetails.

        (Optional) Exchange rate to base currency when money is spent or received. If not specified, XE rate for the day is applied  # noqa: E501

        :param currency_rate: The currency_rate of this BalanceDetails.  # noqa: E501
        :type: float
        """

        self._currency_rate = currency_rate
