# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class Contact(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "contact_id": "str",
        "merged_to_contact_id": "str",
        "contact_number": "str",
        "account_number": "str",
        "contact_status": "str",
        "name": "str",
        "first_name": "str",
        "last_name": "str",
        "company_number": "str",
        "email_address": "str",
        "contact_persons": "list[<PERSON><PERSON>erson]",
        "bank_account_details": "str",
        "tax_number": "str",
        "accounts_receivable_tax_type": "str",
        "accounts_payable_tax_type": "str",
        "addresses": "list[Address]",
        "phones": "list[Phone]",
        "is_supplier": "bool",
        "is_customer": "bool",
        "sales_default_line_amount_type": "str",
        "purchases_default_line_amount_type": "str",
        "default_currency": "CurrencyCode",
        "xero_network_key": "str",
        "sales_default_account_code": "str",
        "purchases_default_account_code": "str",
        "sales_tracking_categories": "list[SalesTrackingCategory]",
        "purchases_tracking_categories": "list[SalesTrackingCategory]",
        "tracking_category_name": "str",
        "tracking_category_option": "str",
        "payment_terms": "PaymentTerm",
        "updated_date_utc": "datetime[ms-format]",
        "contact_groups": "list[ContactGroup]",
        "website": "str",
        "branding_theme": "BrandingTheme",
        "batch_payments": "BatchPaymentDetails",
        "discount": "float",
        "balances": "Balances",
        "attachments": "list[Attachment]",
        "has_attachments": "bool",
        "validation_errors": "list[ValidationError]",
        "has_validation_errors": "bool",
        "status_attribute_string": "str",
    }

    attribute_map = {
        "contact_id": "ContactID",
        "merged_to_contact_id": "MergedToContactID",
        "contact_number": "ContactNumber",
        "account_number": "AccountNumber",
        "contact_status": "ContactStatus",
        "name": "Name",
        "first_name": "FirstName",
        "last_name": "LastName",
        "company_number": "CompanyNumber",
        "email_address": "EmailAddress",
        "contact_persons": "ContactPersons",
        "bank_account_details": "BankAccountDetails",
        "tax_number": "TaxNumber",
        "accounts_receivable_tax_type": "AccountsReceivableTaxType",
        "accounts_payable_tax_type": "AccountsPayableTaxType",
        "addresses": "Addresses",
        "phones": "Phones",
        "is_supplier": "IsSupplier",
        "is_customer": "IsCustomer",
        "sales_default_line_amount_type": "SalesDefaultLineAmountType",
        "purchases_default_line_amount_type": "PurchasesDefaultLineAmountType",
        "default_currency": "DefaultCurrency",
        "xero_network_key": "XeroNetworkKey",
        "sales_default_account_code": "SalesDefaultAccountCode",
        "purchases_default_account_code": "PurchasesDefaultAccountCode",
        "sales_tracking_categories": "SalesTrackingCategories",
        "purchases_tracking_categories": "PurchasesTrackingCategories",
        "tracking_category_name": "TrackingCategoryName",
        "tracking_category_option": "TrackingCategoryOption",
        "payment_terms": "PaymentTerms",
        "updated_date_utc": "UpdatedDateUTC",
        "contact_groups": "ContactGroups",
        "website": "Website",
        "branding_theme": "BrandingTheme",
        "batch_payments": "BatchPayments",
        "discount": "Discount",
        "balances": "Balances",
        "attachments": "Attachments",
        "has_attachments": "HasAttachments",
        "validation_errors": "ValidationErrors",
        "has_validation_errors": "HasValidationErrors",
        "status_attribute_string": "StatusAttributeString",
    }

    def __init__(
        self,
        contact_id=None,
        merged_to_contact_id=None,
        contact_number=None,
        account_number=None,
        contact_status=None,
        name=None,
        first_name=None,
        last_name=None,
        company_number=None,
        email_address=None,
        contact_persons=None,
        bank_account_details=None,
        tax_number=None,
        accounts_receivable_tax_type=None,
        accounts_payable_tax_type=None,
        addresses=None,
        phones=None,
        is_supplier=None,
        is_customer=None,
        sales_default_line_amount_type=None,
        purchases_default_line_amount_type=None,
        default_currency=None,
        xero_network_key=None,
        sales_default_account_code=None,
        purchases_default_account_code=None,
        sales_tracking_categories=None,
        purchases_tracking_categories=None,
        tracking_category_name=None,
        tracking_category_option=None,
        payment_terms=None,
        updated_date_utc=None,
        contact_groups=None,
        website=None,
        branding_theme=None,
        batch_payments=None,
        discount=None,
        balances=None,
        attachments=None,
        has_attachments=False,
        validation_errors=None,
        has_validation_errors=False,
        status_attribute_string=None,
    ):  # noqa: E501
        """Contact - a model defined in OpenAPI"""  # noqa: E501

        self._contact_id = None
        self._merged_to_contact_id = None
        self._contact_number = None
        self._account_number = None
        self._contact_status = None
        self._name = None
        self._first_name = None
        self._last_name = None
        self._company_number = None
        self._email_address = None
        self._contact_persons = None
        self._bank_account_details = None
        self._tax_number = None
        self._accounts_receivable_tax_type = None
        self._accounts_payable_tax_type = None
        self._addresses = None
        self._phones = None
        self._is_supplier = None
        self._is_customer = None
        self._sales_default_line_amount_type = None
        self._purchases_default_line_amount_type = None
        self._default_currency = None
        self._xero_network_key = None
        self._sales_default_account_code = None
        self._purchases_default_account_code = None
        self._sales_tracking_categories = None
        self._purchases_tracking_categories = None
        self._tracking_category_name = None
        self._tracking_category_option = None
        self._payment_terms = None
        self._updated_date_utc = None
        self._contact_groups = None
        self._website = None
        self._branding_theme = None
        self._batch_payments = None
        self._discount = None
        self._balances = None
        self._attachments = None
        self._has_attachments = None
        self._validation_errors = None
        self._has_validation_errors = None
        self._status_attribute_string = None
        self.discriminator = None

        if contact_id is not None:
            self.contact_id = contact_id
        if merged_to_contact_id is not None:
            self.merged_to_contact_id = merged_to_contact_id
        if contact_number is not None:
            self.contact_number = contact_number
        if account_number is not None:
            self.account_number = account_number
        if contact_status is not None:
            self.contact_status = contact_status
        if name is not None:
            self.name = name
        if first_name is not None:
            self.first_name = first_name
        if last_name is not None:
            self.last_name = last_name
        if company_number is not None:
            self.company_number = company_number
        if email_address is not None:
            self.email_address = email_address
        if contact_persons is not None:
            self.contact_persons = contact_persons
        if bank_account_details is not None:
            self.bank_account_details = bank_account_details
        if tax_number is not None:
            self.tax_number = tax_number
        if accounts_receivable_tax_type is not None:
            self.accounts_receivable_tax_type = accounts_receivable_tax_type
        if accounts_payable_tax_type is not None:
            self.accounts_payable_tax_type = accounts_payable_tax_type
        if addresses is not None:
            self.addresses = addresses
        if phones is not None:
            self.phones = phones
        if is_supplier is not None:
            self.is_supplier = is_supplier
        if is_customer is not None:
            self.is_customer = is_customer
        if sales_default_line_amount_type is not None:
            self.sales_default_line_amount_type = sales_default_line_amount_type
        if purchases_default_line_amount_type is not None:
            self.purchases_default_line_amount_type = purchases_default_line_amount_type
        if default_currency is not None:
            self.default_currency = default_currency
        if xero_network_key is not None:
            self.xero_network_key = xero_network_key
        if sales_default_account_code is not None:
            self.sales_default_account_code = sales_default_account_code
        if purchases_default_account_code is not None:
            self.purchases_default_account_code = purchases_default_account_code
        if sales_tracking_categories is not None:
            self.sales_tracking_categories = sales_tracking_categories
        if purchases_tracking_categories is not None:
            self.purchases_tracking_categories = purchases_tracking_categories
        if tracking_category_name is not None:
            self.tracking_category_name = tracking_category_name
        if tracking_category_option is not None:
            self.tracking_category_option = tracking_category_option
        if payment_terms is not None:
            self.payment_terms = payment_terms
        if updated_date_utc is not None:
            self.updated_date_utc = updated_date_utc
        if contact_groups is not None:
            self.contact_groups = contact_groups
        if website is not None:
            self.website = website
        if branding_theme is not None:
            self.branding_theme = branding_theme
        if batch_payments is not None:
            self.batch_payments = batch_payments
        if discount is not None:
            self.discount = discount
        if balances is not None:
            self.balances = balances
        if attachments is not None:
            self.attachments = attachments
        if has_attachments is not None:
            self.has_attachments = has_attachments
        if validation_errors is not None:
            self.validation_errors = validation_errors
        if has_validation_errors is not None:
            self.has_validation_errors = has_validation_errors
        if status_attribute_string is not None:
            self.status_attribute_string = status_attribute_string

    @property
    def contact_id(self):
        """Gets the contact_id of this Contact.  # noqa: E501

        Xero identifier  # noqa: E501

        :return: The contact_id of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._contact_id

    @contact_id.setter
    def contact_id(self, contact_id):
        """Sets the contact_id of this Contact.

        Xero identifier  # noqa: E501

        :param contact_id: The contact_id of this Contact.  # noqa: E501
        :type: str
        """

        self._contact_id = contact_id

    @property
    def merged_to_contact_id(self):
        """Gets the merged_to_contact_id of this Contact.  # noqa: E501

        ID for the destination of a merged contact. Only returned when using paging or when fetching a contact by ContactId or ContactNumber.  # noqa: E501

        :return: The merged_to_contact_id of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._merged_to_contact_id

    @merged_to_contact_id.setter
    def merged_to_contact_id(self, merged_to_contact_id):
        """Sets the merged_to_contact_id of this Contact.

        ID for the destination of a merged contact. Only returned when using paging or when fetching a contact by ContactId or ContactNumber.  # noqa: E501

        :param merged_to_contact_id: The merged_to_contact_id of this Contact.  # noqa: E501
        :type: str
        """

        self._merged_to_contact_id = merged_to_contact_id

    @property
    def contact_number(self):
        """Gets the contact_number of this Contact.  # noqa: E501

        This can be updated via the API only i.e. This field is read only on the Xero contact screen, used to identify contacts in external systems (max length = 50). If the Contact Number is used, this is displayed as Contact Code in the Contacts UI in Xero.  # noqa: E501

        :return: The contact_number of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._contact_number

    @contact_number.setter
    def contact_number(self, contact_number):
        """Sets the contact_number of this Contact.

        This can be updated via the API only i.e. This field is read only on the Xero contact screen, used to identify contacts in external systems (max length = 50). If the Contact Number is used, this is displayed as Contact Code in the Contacts UI in Xero.  # noqa: E501

        :param contact_number: The contact_number of this Contact.  # noqa: E501
        :type: str
        """
        if contact_number is not None and len(contact_number) > 50:
            raise ValueError(
                "Invalid value for `contact_number`, "
                "length must be less than or equal to `50`"
            )  # noqa: E501

        self._contact_number = contact_number

    @property
    def account_number(self):
        """Gets the account_number of this Contact.  # noqa: E501

        A user defined account number. This can be updated via the API and the Xero UI (max length = 50)  # noqa: E501

        :return: The account_number of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._account_number

    @account_number.setter
    def account_number(self, account_number):
        """Sets the account_number of this Contact.

        A user defined account number. This can be updated via the API and the Xero UI (max length = 50)  # noqa: E501

        :param account_number: The account_number of this Contact.  # noqa: E501
        :type: str
        """
        if account_number is not None and len(account_number) > 50:
            raise ValueError(
                "Invalid value for `account_number`, "
                "length must be less than or equal to `50`"
            )  # noqa: E501

        self._account_number = account_number

    @property
    def contact_status(self):
        """Gets the contact_status of this Contact.  # noqa: E501

        Current status of a contact – see contact status types  # noqa: E501

        :return: The contact_status of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._contact_status

    @contact_status.setter
    def contact_status(self, contact_status):
        """Sets the contact_status of this Contact.

        Current status of a contact – see contact status types  # noqa: E501

        :param contact_status: The contact_status of this Contact.  # noqa: E501
        :type: str
        """
        allowed_values = ["ACTIVE", "ARCHIVED", "GDPRREQUEST", "None"]  # noqa: E501

        if contact_status:
            if contact_status not in allowed_values:
                raise ValueError(
                    "Invalid value for `contact_status` ({0}), must be one of {1}".format(  # noqa: E501
                        contact_status, allowed_values
                    )
                )

        self._contact_status = contact_status

    @property
    def name(self):
        """Gets the name of this Contact.  # noqa: E501

        Full name of contact/organisation (max length = 255)  # noqa: E501

        :return: The name of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this Contact.

        Full name of contact/organisation (max length = 255)  # noqa: E501

        :param name: The name of this Contact.  # noqa: E501
        :type: str
        """
        if name is not None and len(name) > 255:
            raise ValueError(
                "Invalid value for `name`, "
                "length must be less than or equal to `255`"
            )  # noqa: E501

        self._name = name

    @property
    def first_name(self):
        """Gets the first_name of this Contact.  # noqa: E501

        First name of contact person (max length = 255)  # noqa: E501

        :return: The first_name of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._first_name

    @first_name.setter
    def first_name(self, first_name):
        """Sets the first_name of this Contact.

        First name of contact person (max length = 255)  # noqa: E501

        :param first_name: The first_name of this Contact.  # noqa: E501
        :type: str
        """
        if first_name is not None and len(first_name) > 255:
            raise ValueError(
                "Invalid value for `first_name`, "
                "length must be less than or equal to `255`"
            )  # noqa: E501

        self._first_name = first_name

    @property
    def last_name(self):
        """Gets the last_name of this Contact.  # noqa: E501

        Last name of contact person (max length = 255)  # noqa: E501

        :return: The last_name of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._last_name

    @last_name.setter
    def last_name(self, last_name):
        """Sets the last_name of this Contact.

        Last name of contact person (max length = 255)  # noqa: E501

        :param last_name: The last_name of this Contact.  # noqa: E501
        :type: str
        """
        if last_name is not None and len(last_name) > 255:
            raise ValueError(
                "Invalid value for `last_name`, "
                "length must be less than or equal to `255`"
            )  # noqa: E501

        self._last_name = last_name

    @property
    def company_number(self):
        """Gets the company_number of this Contact.  # noqa: E501

        Company registration number (max length = 50)  # noqa: E501

        :return: The company_number of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._company_number

    @company_number.setter
    def company_number(self, company_number):
        """Sets the company_number of this Contact.

        Company registration number (max length = 50)  # noqa: E501

        :param company_number: The company_number of this Contact.  # noqa: E501
        :type: str
        """
        if company_number is not None and len(company_number) > 50:
            raise ValueError(
                "Invalid value for `company_number`, "
                "length must be less than or equal to `50`"
            )  # noqa: E501

        self._company_number = company_number

    @property
    def email_address(self):
        """Gets the email_address of this Contact.  # noqa: E501

        Email address of contact person (umlauts not supported) (max length  = 255)  # noqa: E501

        :return: The email_address of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._email_address

    @email_address.setter
    def email_address(self, email_address):
        """Sets the email_address of this Contact.

        Email address of contact person (umlauts not supported) (max length  = 255)  # noqa: E501

        :param email_address: The email_address of this Contact.  # noqa: E501
        :type: str
        """
        if email_address is not None and len(email_address) > 255:
            raise ValueError(
                "Invalid value for `email_address`, "
                "length must be less than or equal to `255`"
            )  # noqa: E501

        self._email_address = email_address

    @property
    def contact_persons(self):
        """Gets the contact_persons of this Contact.  # noqa: E501

        See contact persons  # noqa: E501

        :return: The contact_persons of this Contact.  # noqa: E501
        :rtype: list[ContactPerson]
        """
        return self._contact_persons

    @contact_persons.setter
    def contact_persons(self, contact_persons):
        """Sets the contact_persons of this Contact.

        See contact persons  # noqa: E501

        :param contact_persons: The contact_persons of this Contact.  # noqa: E501
        :type: list[ContactPerson]
        """

        self._contact_persons = contact_persons

    @property
    def bank_account_details(self):
        """Gets the bank_account_details of this Contact.  # noqa: E501

        Bank account number of contact  # noqa: E501

        :return: The bank_account_details of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._bank_account_details

    @bank_account_details.setter
    def bank_account_details(self, bank_account_details):
        """Sets the bank_account_details of this Contact.

        Bank account number of contact  # noqa: E501

        :param bank_account_details: The bank_account_details of this Contact.  # noqa: E501
        :type: str
        """

        self._bank_account_details = bank_account_details

    @property
    def tax_number(self):
        """Gets the tax_number of this Contact.  # noqa: E501

        Tax number of contact – this is also known as the ABN (Australia), GST Number (New Zealand), VAT Number (UK) or Tax ID Number (US and global) in the Xero UI depending on which regionalized version of Xero you are using (max length = 50)  # noqa: E501

        :return: The tax_number of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._tax_number

    @tax_number.setter
    def tax_number(self, tax_number):
        """Sets the tax_number of this Contact.

        Tax number of contact – this is also known as the ABN (Australia), GST Number (New Zealand), VAT Number (UK) or Tax ID Number (US and global) in the Xero UI depending on which regionalized version of Xero you are using (max length = 50)  # noqa: E501

        :param tax_number: The tax_number of this Contact.  # noqa: E501
        :type: str
        """
        if tax_number is not None and len(tax_number) > 50:
            raise ValueError(
                "Invalid value for `tax_number`, "
                "length must be less than or equal to `50`"
            )  # noqa: E501

        self._tax_number = tax_number

    @property
    def accounts_receivable_tax_type(self):
        """Gets the accounts_receivable_tax_type of this Contact.  # noqa: E501

        The tax type from TaxRates  # noqa: E501

        :return: The accounts_receivable_tax_type of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._accounts_receivable_tax_type

    @accounts_receivable_tax_type.setter
    def accounts_receivable_tax_type(self, accounts_receivable_tax_type):
        """Sets the accounts_receivable_tax_type of this Contact.

        The tax type from TaxRates  # noqa: E501

        :param accounts_receivable_tax_type: The accounts_receivable_tax_type of this Contact.  # noqa: E501
        :type: str
        """

        self._accounts_receivable_tax_type = accounts_receivable_tax_type

    @property
    def accounts_payable_tax_type(self):
        """Gets the accounts_payable_tax_type of this Contact.  # noqa: E501

        The tax type from TaxRates  # noqa: E501

        :return: The accounts_payable_tax_type of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._accounts_payable_tax_type

    @accounts_payable_tax_type.setter
    def accounts_payable_tax_type(self, accounts_payable_tax_type):
        """Sets the accounts_payable_tax_type of this Contact.

        The tax type from TaxRates  # noqa: E501

        :param accounts_payable_tax_type: The accounts_payable_tax_type of this Contact.  # noqa: E501
        :type: str
        """

        self._accounts_payable_tax_type = accounts_payable_tax_type

    @property
    def addresses(self):
        """Gets the addresses of this Contact.  # noqa: E501

        Store certain address types for a contact – see address types  # noqa: E501

        :return: The addresses of this Contact.  # noqa: E501
        :rtype: list[Address]
        """
        return self._addresses

    @addresses.setter
    def addresses(self, addresses):
        """Sets the addresses of this Contact.

        Store certain address types for a contact – see address types  # noqa: E501

        :param addresses: The addresses of this Contact.  # noqa: E501
        :type: list[Address]
        """

        self._addresses = addresses

    @property
    def phones(self):
        """Gets the phones of this Contact.  # noqa: E501

        Store certain phone types for a contact – see phone types  # noqa: E501

        :return: The phones of this Contact.  # noqa: E501
        :rtype: list[Phone]
        """
        return self._phones

    @phones.setter
    def phones(self, phones):
        """Sets the phones of this Contact.

        Store certain phone types for a contact – see phone types  # noqa: E501

        :param phones: The phones of this Contact.  # noqa: E501
        :type: list[Phone]
        """

        self._phones = phones

    @property
    def is_supplier(self):
        """Gets the is_supplier of this Contact.  # noqa: E501

        true or false – Boolean that describes if a contact that has any AP  invoices entered against them. Cannot be set via PUT or POST – it is automatically set when an accounts payable invoice is generated against this contact.  # noqa: E501

        :return: The is_supplier of this Contact.  # noqa: E501
        :rtype: bool
        """
        return self._is_supplier

    @is_supplier.setter
    def is_supplier(self, is_supplier):
        """Sets the is_supplier of this Contact.

        true or false – Boolean that describes if a contact that has any AP  invoices entered against them. Cannot be set via PUT or POST – it is automatically set when an accounts payable invoice is generated against this contact.  # noqa: E501

        :param is_supplier: The is_supplier of this Contact.  # noqa: E501
        :type: bool
        """

        self._is_supplier = is_supplier

    @property
    def is_customer(self):
        """Gets the is_customer of this Contact.  # noqa: E501

        true or false – Boolean that describes if a contact has any AR invoices entered against them. Cannot be set via PUT or POST – it is automatically set when an accounts receivable invoice is generated against this contact.  # noqa: E501

        :return: The is_customer of this Contact.  # noqa: E501
        :rtype: bool
        """
        return self._is_customer

    @is_customer.setter
    def is_customer(self, is_customer):
        """Sets the is_customer of this Contact.

        true or false – Boolean that describes if a contact has any AR invoices entered against them. Cannot be set via PUT or POST – it is automatically set when an accounts receivable invoice is generated against this contact.  # noqa: E501

        :param is_customer: The is_customer of this Contact.  # noqa: E501
        :type: bool
        """

        self._is_customer = is_customer

    @property
    def sales_default_line_amount_type(self):
        """Gets the sales_default_line_amount_type of this Contact.  # noqa: E501

        The default sales line amount type for a contact. Only available when summaryOnly parameter or paging is used, or when fetch by ContactId or ContactNumber.  # noqa: E501

        :return: The sales_default_line_amount_type of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._sales_default_line_amount_type

    @sales_default_line_amount_type.setter
    def sales_default_line_amount_type(self, sales_default_line_amount_type):
        """Sets the sales_default_line_amount_type of this Contact.

        The default sales line amount type for a contact. Only available when summaryOnly parameter or paging is used, or when fetch by ContactId or ContactNumber.  # noqa: E501

        :param sales_default_line_amount_type: The sales_default_line_amount_type of this Contact.  # noqa: E501
        :type: str
        """
        allowed_values = ["INCLUSIVE", "EXCLUSIVE", "NONE", "None"]  # noqa: E501

        if sales_default_line_amount_type:
            if sales_default_line_amount_type not in allowed_values:
                raise ValueError(
                    "Invalid value for `sales_default_line_amount_type` ({0}), must be one of {1}".format(  # noqa: E501
                        sales_default_line_amount_type, allowed_values
                    )
                )

        self._sales_default_line_amount_type = sales_default_line_amount_type

    @property
    def purchases_default_line_amount_type(self):
        """Gets the purchases_default_line_amount_type of this Contact.  # noqa: E501

        The default purchases line amount type for a contact Only available when summaryOnly parameter or paging is used, or when fetch by ContactId or ContactNumber.  # noqa: E501

        :return: The purchases_default_line_amount_type of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._purchases_default_line_amount_type

    @purchases_default_line_amount_type.setter
    def purchases_default_line_amount_type(self, purchases_default_line_amount_type):
        """Sets the purchases_default_line_amount_type of this Contact.

        The default purchases line amount type for a contact Only available when summaryOnly parameter or paging is used, or when fetch by ContactId or ContactNumber.  # noqa: E501

        :param purchases_default_line_amount_type: The purchases_default_line_amount_type of this Contact.  # noqa: E501
        :type: str
        """
        allowed_values = ["INCLUSIVE", "EXCLUSIVE", "NONE", "None"]  # noqa: E501

        if purchases_default_line_amount_type:
            if purchases_default_line_amount_type not in allowed_values:
                raise ValueError(
                    "Invalid value for `purchases_default_line_amount_type` ({0}), must be one of {1}".format(  # noqa: E501
                        purchases_default_line_amount_type, allowed_values
                    )
                )

        self._purchases_default_line_amount_type = purchases_default_line_amount_type

    @property
    def default_currency(self):
        """Gets the default_currency of this Contact.  # noqa: E501


        :return: The default_currency of this Contact.  # noqa: E501
        :rtype: CurrencyCode
        """
        return self._default_currency

    @default_currency.setter
    def default_currency(self, default_currency):
        """Sets the default_currency of this Contact.


        :param default_currency: The default_currency of this Contact.  # noqa: E501
        :type: CurrencyCode
        """

        self._default_currency = default_currency

    @property
    def xero_network_key(self):
        """Gets the xero_network_key of this Contact.  # noqa: E501

        Store XeroNetworkKey for contacts.  # noqa: E501

        :return: The xero_network_key of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._xero_network_key

    @xero_network_key.setter
    def xero_network_key(self, xero_network_key):
        """Sets the xero_network_key of this Contact.

        Store XeroNetworkKey for contacts.  # noqa: E501

        :param xero_network_key: The xero_network_key of this Contact.  # noqa: E501
        :type: str
        """

        self._xero_network_key = xero_network_key

    @property
    def sales_default_account_code(self):
        """Gets the sales_default_account_code of this Contact.  # noqa: E501

        The default sales account code for contacts  # noqa: E501

        :return: The sales_default_account_code of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._sales_default_account_code

    @sales_default_account_code.setter
    def sales_default_account_code(self, sales_default_account_code):
        """Sets the sales_default_account_code of this Contact.

        The default sales account code for contacts  # noqa: E501

        :param sales_default_account_code: The sales_default_account_code of this Contact.  # noqa: E501
        :type: str
        """

        self._sales_default_account_code = sales_default_account_code

    @property
    def purchases_default_account_code(self):
        """Gets the purchases_default_account_code of this Contact.  # noqa: E501

        The default purchases account code for contacts  # noqa: E501

        :return: The purchases_default_account_code of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._purchases_default_account_code

    @purchases_default_account_code.setter
    def purchases_default_account_code(self, purchases_default_account_code):
        """Sets the purchases_default_account_code of this Contact.

        The default purchases account code for contacts  # noqa: E501

        :param purchases_default_account_code: The purchases_default_account_code of this Contact.  # noqa: E501
        :type: str
        """

        self._purchases_default_account_code = purchases_default_account_code

    @property
    def sales_tracking_categories(self):
        """Gets the sales_tracking_categories of this Contact.  # noqa: E501

        The default sales tracking categories for contacts  # noqa: E501

        :return: The sales_tracking_categories of this Contact.  # noqa: E501
        :rtype: list[SalesTrackingCategory]
        """
        return self._sales_tracking_categories

    @sales_tracking_categories.setter
    def sales_tracking_categories(self, sales_tracking_categories):
        """Sets the sales_tracking_categories of this Contact.

        The default sales tracking categories for contacts  # noqa: E501

        :param sales_tracking_categories: The sales_tracking_categories of this Contact.  # noqa: E501
        :type: list[SalesTrackingCategory]
        """

        self._sales_tracking_categories = sales_tracking_categories

    @property
    def purchases_tracking_categories(self):
        """Gets the purchases_tracking_categories of this Contact.  # noqa: E501

        The default purchases tracking categories for contacts  # noqa: E501

        :return: The purchases_tracking_categories of this Contact.  # noqa: E501
        :rtype: list[SalesTrackingCategory]
        """
        return self._purchases_tracking_categories

    @purchases_tracking_categories.setter
    def purchases_tracking_categories(self, purchases_tracking_categories):
        """Sets the purchases_tracking_categories of this Contact.

        The default purchases tracking categories for contacts  # noqa: E501

        :param purchases_tracking_categories: The purchases_tracking_categories of this Contact.  # noqa: E501
        :type: list[SalesTrackingCategory]
        """

        self._purchases_tracking_categories = purchases_tracking_categories

    @property
    def tracking_category_name(self):
        """Gets the tracking_category_name of this Contact.  # noqa: E501

        The name of the Tracking Category assigned to the contact under SalesTrackingCategories and PurchasesTrackingCategories  # noqa: E501

        :return: The tracking_category_name of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._tracking_category_name

    @tracking_category_name.setter
    def tracking_category_name(self, tracking_category_name):
        """Sets the tracking_category_name of this Contact.

        The name of the Tracking Category assigned to the contact under SalesTrackingCategories and PurchasesTrackingCategories  # noqa: E501

        :param tracking_category_name: The tracking_category_name of this Contact.  # noqa: E501
        :type: str
        """

        self._tracking_category_name = tracking_category_name

    @property
    def tracking_category_option(self):
        """Gets the tracking_category_option of this Contact.  # noqa: E501

        The name of the Tracking Option assigned to the contact under SalesTrackingCategories and PurchasesTrackingCategories  # noqa: E501

        :return: The tracking_category_option of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._tracking_category_option

    @tracking_category_option.setter
    def tracking_category_option(self, tracking_category_option):
        """Sets the tracking_category_option of this Contact.

        The name of the Tracking Option assigned to the contact under SalesTrackingCategories and PurchasesTrackingCategories  # noqa: E501

        :param tracking_category_option: The tracking_category_option of this Contact.  # noqa: E501
        :type: str
        """

        self._tracking_category_option = tracking_category_option

    @property
    def payment_terms(self):
        """Gets the payment_terms of this Contact.  # noqa: E501


        :return: The payment_terms of this Contact.  # noqa: E501
        :rtype: PaymentTerm
        """
        return self._payment_terms

    @payment_terms.setter
    def payment_terms(self, payment_terms):
        """Sets the payment_terms of this Contact.


        :param payment_terms: The payment_terms of this Contact.  # noqa: E501
        :type: PaymentTerm
        """

        self._payment_terms = payment_terms

    @property
    def updated_date_utc(self):
        """Gets the updated_date_utc of this Contact.  # noqa: E501

        UTC timestamp of last update to contact  # noqa: E501

        :return: The updated_date_utc of this Contact.  # noqa: E501
        :rtype: datetime
        """
        return self._updated_date_utc

    @updated_date_utc.setter
    def updated_date_utc(self, updated_date_utc):
        """Sets the updated_date_utc of this Contact.

        UTC timestamp of last update to contact  # noqa: E501

        :param updated_date_utc: The updated_date_utc of this Contact.  # noqa: E501
        :type: datetime
        """

        self._updated_date_utc = updated_date_utc

    @property
    def contact_groups(self):
        """Gets the contact_groups of this Contact.  # noqa: E501

        Displays which contact groups a contact is included in  # noqa: E501

        :return: The contact_groups of this Contact.  # noqa: E501
        :rtype: list[ContactGroup]
        """
        return self._contact_groups

    @contact_groups.setter
    def contact_groups(self, contact_groups):
        """Sets the contact_groups of this Contact.

        Displays which contact groups a contact is included in  # noqa: E501

        :param contact_groups: The contact_groups of this Contact.  # noqa: E501
        :type: list[ContactGroup]
        """

        self._contact_groups = contact_groups

    @property
    def website(self):
        """Gets the website of this Contact.  # noqa: E501

        Website address for contact (read only)  # noqa: E501

        :return: The website of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._website

    @website.setter
    def website(self, website):
        """Sets the website of this Contact.

        Website address for contact (read only)  # noqa: E501

        :param website: The website of this Contact.  # noqa: E501
        :type: str
        """

        self._website = website

    @property
    def branding_theme(self):
        """Gets the branding_theme of this Contact.  # noqa: E501


        :return: The branding_theme of this Contact.  # noqa: E501
        :rtype: BrandingTheme
        """
        return self._branding_theme

    @branding_theme.setter
    def branding_theme(self, branding_theme):
        """Sets the branding_theme of this Contact.


        :param branding_theme: The branding_theme of this Contact.  # noqa: E501
        :type: BrandingTheme
        """

        self._branding_theme = branding_theme

    @property
    def batch_payments(self):
        """Gets the batch_payments of this Contact.  # noqa: E501


        :return: The batch_payments of this Contact.  # noqa: E501
        :rtype: BatchPaymentDetails
        """
        return self._batch_payments

    @batch_payments.setter
    def batch_payments(self, batch_payments):
        """Sets the batch_payments of this Contact.


        :param batch_payments: The batch_payments of this Contact.  # noqa: E501
        :type: BatchPaymentDetails
        """

        self._batch_payments = batch_payments

    @property
    def discount(self):
        """Gets the discount of this Contact.  # noqa: E501

        The default discount rate for the contact (read only)  # noqa: E501

        :return: The discount of this Contact.  # noqa: E501
        :rtype: float
        """
        return self._discount

    @discount.setter
    def discount(self, discount):
        """Sets the discount of this Contact.

        The default discount rate for the contact (read only)  # noqa: E501

        :param discount: The discount of this Contact.  # noqa: E501
        :type: float
        """

        self._discount = discount

    @property
    def balances(self):
        """Gets the balances of this Contact.  # noqa: E501


        :return: The balances of this Contact.  # noqa: E501
        :rtype: Balances
        """
        return self._balances

    @balances.setter
    def balances(self, balances):
        """Sets the balances of this Contact.


        :param balances: The balances of this Contact.  # noqa: E501
        :type: Balances
        """

        self._balances = balances

    @property
    def attachments(self):
        """Gets the attachments of this Contact.  # noqa: E501

        Displays array of attachments from the API  # noqa: E501

        :return: The attachments of this Contact.  # noqa: E501
        :rtype: list[Attachment]
        """
        return self._attachments

    @attachments.setter
    def attachments(self, attachments):
        """Sets the attachments of this Contact.

        Displays array of attachments from the API  # noqa: E501

        :param attachments: The attachments of this Contact.  # noqa: E501
        :type: list[Attachment]
        """

        self._attachments = attachments

    @property
    def has_attachments(self):
        """Gets the has_attachments of this Contact.  # noqa: E501

        A boolean to indicate if a contact has an attachment  # noqa: E501

        :return: The has_attachments of this Contact.  # noqa: E501
        :rtype: bool
        """
        return self._has_attachments

    @has_attachments.setter
    def has_attachments(self, has_attachments):
        """Sets the has_attachments of this Contact.

        A boolean to indicate if a contact has an attachment  # noqa: E501

        :param has_attachments: The has_attachments of this Contact.  # noqa: E501
        :type: bool
        """

        self._has_attachments = has_attachments

    @property
    def validation_errors(self):
        """Gets the validation_errors of this Contact.  # noqa: E501

        Displays validation errors returned from the API  # noqa: E501

        :return: The validation_errors of this Contact.  # noqa: E501
        :rtype: list[ValidationError]
        """
        return self._validation_errors

    @validation_errors.setter
    def validation_errors(self, validation_errors):
        """Sets the validation_errors of this Contact.

        Displays validation errors returned from the API  # noqa: E501

        :param validation_errors: The validation_errors of this Contact.  # noqa: E501
        :type: list[ValidationError]
        """

        self._validation_errors = validation_errors

    @property
    def has_validation_errors(self):
        """Gets the has_validation_errors of this Contact.  # noqa: E501

        A boolean to indicate if a contact has an validation errors  # noqa: E501

        :return: The has_validation_errors of this Contact.  # noqa: E501
        :rtype: bool
        """
        return self._has_validation_errors

    @has_validation_errors.setter
    def has_validation_errors(self, has_validation_errors):
        """Sets the has_validation_errors of this Contact.

        A boolean to indicate if a contact has an validation errors  # noqa: E501

        :param has_validation_errors: The has_validation_errors of this Contact.  # noqa: E501
        :type: bool
        """

        self._has_validation_errors = has_validation_errors

    @property
    def status_attribute_string(self):
        """Gets the status_attribute_string of this Contact.  # noqa: E501

        Status of object  # noqa: E501

        :return: The status_attribute_string of this Contact.  # noqa: E501
        :rtype: str
        """
        return self._status_attribute_string

    @status_attribute_string.setter
    def status_attribute_string(self, status_attribute_string):
        """Sets the status_attribute_string of this Contact.

        Status of object  # noqa: E501

        :param status_attribute_string: The status_attribute_string of this Contact.  # noqa: E501
        :type: str
        """

        self._status_attribute_string = status_attribute_string
