# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class BatchPaymentDelete(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {"batch_payment_id": "str", "status": "str"}

    attribute_map = {"batch_payment_id": "BatchPaymentID", "status": "Status"}

    def __init__(self, batch_payment_id=None, status="DELETED"):  # noqa: E501
        """BatchPaymentDelete - a model defined in OpenAPI"""  # noqa: E501

        self._batch_payment_id = None
        self._status = None
        self.discriminator = None

        self.batch_payment_id = batch_payment_id
        self.status = status

    @property
    def batch_payment_id(self):
        """Gets the batch_payment_id of this BatchPaymentDelete.  # noqa: E501

        The Xero generated unique identifier for the bank transaction (read-only)  # noqa: E501

        :return: The batch_payment_id of this BatchPaymentDelete.  # noqa: E501
        :rtype: str
        """
        return self._batch_payment_id

    @batch_payment_id.setter
    def batch_payment_id(self, batch_payment_id):
        """Sets the batch_payment_id of this BatchPaymentDelete.

        The Xero generated unique identifier for the bank transaction (read-only)  # noqa: E501

        :param batch_payment_id: The batch_payment_id of this BatchPaymentDelete.  # noqa: E501
        :type: str
        """
        if batch_payment_id is None:
            raise ValueError(
                "Invalid value for `batch_payment_id`, must not be `None`"
            )  # noqa: E501

        self._batch_payment_id = batch_payment_id

    @property
    def status(self):
        """Gets the status of this BatchPaymentDelete.  # noqa: E501

        The status of the batch payment.  # noqa: E501

        :return: The status of this BatchPaymentDelete.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this BatchPaymentDelete.

        The status of the batch payment.  # noqa: E501

        :param status: The status of this BatchPaymentDelete.  # noqa: E501
        :type: str
        """
        if status is None:
            raise ValueError(
                "Invalid value for `status`, must not be `None`"
            )  # noqa: E501

        self._status = status
