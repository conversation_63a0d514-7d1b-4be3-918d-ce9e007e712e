# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class Actions(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {"actions": "list[Action]"}

    attribute_map = {"actions": "Actions"}

    def __init__(self, actions=None):  # noqa: E501
        """Actions - a model defined in OpenAPI"""  # noqa: E501

        self._actions = None
        self.discriminator = None

        if actions is not None:
            self.actions = actions

    @property
    def actions(self):
        """Gets the actions of this Actions.  # noqa: E501


        :return: The actions of this Actions.  # noqa: E501
        :rtype: list[Action]
        """
        return self._actions

    @actions.setter
    def actions(self, actions):
        """Sets the actions of this Actions.


        :param actions: The actions of this Actions.  # noqa: E501
        :type: list[Action]
        """

        self._actions = actions
