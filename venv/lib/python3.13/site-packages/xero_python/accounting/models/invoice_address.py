# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class InvoiceAddress(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "invoice_address_type": "str",
        "address_line1": "str",
        "address_line2": "str",
        "address_line3": "str",
        "address_line4": "str",
        "city": "str",
        "region": "str",
        "postal_code": "str",
        "country": "str",
    }

    attribute_map = {
        "invoice_address_type": "InvoiceAddressType",
        "address_line1": "AddressLine1",
        "address_line2": "AddressLine2",
        "address_line3": "AddressLine3",
        "address_line4": "AddressLine4",
        "city": "City",
        "region": "Region",
        "postal_code": "PostalCode",
        "country": "Country",
    }

    def __init__(
        self,
        invoice_address_type=None,
        address_line1=None,
        address_line2=None,
        address_line3=None,
        address_line4=None,
        city=None,
        region=None,
        postal_code=None,
        country=None,
    ):  # noqa: E501
        """InvoiceAddress - a model defined in OpenAPI"""  # noqa: E501

        self._invoice_address_type = None
        self._address_line1 = None
        self._address_line2 = None
        self._address_line3 = None
        self._address_line4 = None
        self._city = None
        self._region = None
        self._postal_code = None
        self._country = None
        self.discriminator = None

        if invoice_address_type is not None:
            self.invoice_address_type = invoice_address_type
        if address_line1 is not None:
            self.address_line1 = address_line1
        if address_line2 is not None:
            self.address_line2 = address_line2
        if address_line3 is not None:
            self.address_line3 = address_line3
        if address_line4 is not None:
            self.address_line4 = address_line4
        if city is not None:
            self.city = city
        if region is not None:
            self.region = region
        if postal_code is not None:
            self.postal_code = postal_code
        if country is not None:
            self.country = country

    @property
    def invoice_address_type(self):
        """Gets the invoice_address_type of this InvoiceAddress.  # noqa: E501

        Indicates whether the address is defined as origin (FROM) or destination (TO)  # noqa: E501

        :return: The invoice_address_type of this InvoiceAddress.  # noqa: E501
        :rtype: str
        """
        return self._invoice_address_type

    @invoice_address_type.setter
    def invoice_address_type(self, invoice_address_type):
        """Sets the invoice_address_type of this InvoiceAddress.

        Indicates whether the address is defined as origin (FROM) or destination (TO)  # noqa: E501

        :param invoice_address_type: The invoice_address_type of this InvoiceAddress.  # noqa: E501
        :type: str
        """
        allowed_values = ["FROM", "TO", "None"]  # noqa: E501

        if invoice_address_type:
            if invoice_address_type not in allowed_values:
                raise ValueError(
                    "Invalid value for `invoice_address_type` ({0}), must be one of {1}".format(  # noqa: E501
                        invoice_address_type, allowed_values
                    )
                )

        self._invoice_address_type = invoice_address_type

    @property
    def address_line1(self):
        """Gets the address_line1 of this InvoiceAddress.  # noqa: E501

        First line of a physical address  # noqa: E501

        :return: The address_line1 of this InvoiceAddress.  # noqa: E501
        :rtype: str
        """
        return self._address_line1

    @address_line1.setter
    def address_line1(self, address_line1):
        """Sets the address_line1 of this InvoiceAddress.

        First line of a physical address  # noqa: E501

        :param address_line1: The address_line1 of this InvoiceAddress.  # noqa: E501
        :type: str
        """

        self._address_line1 = address_line1

    @property
    def address_line2(self):
        """Gets the address_line2 of this InvoiceAddress.  # noqa: E501

        Second line of a physical address  # noqa: E501

        :return: The address_line2 of this InvoiceAddress.  # noqa: E501
        :rtype: str
        """
        return self._address_line2

    @address_line2.setter
    def address_line2(self, address_line2):
        """Sets the address_line2 of this InvoiceAddress.

        Second line of a physical address  # noqa: E501

        :param address_line2: The address_line2 of this InvoiceAddress.  # noqa: E501
        :type: str
        """

        self._address_line2 = address_line2

    @property
    def address_line3(self):
        """Gets the address_line3 of this InvoiceAddress.  # noqa: E501

        Third line of a physical address  # noqa: E501

        :return: The address_line3 of this InvoiceAddress.  # noqa: E501
        :rtype: str
        """
        return self._address_line3

    @address_line3.setter
    def address_line3(self, address_line3):
        """Sets the address_line3 of this InvoiceAddress.

        Third line of a physical address  # noqa: E501

        :param address_line3: The address_line3 of this InvoiceAddress.  # noqa: E501
        :type: str
        """

        self._address_line3 = address_line3

    @property
    def address_line4(self):
        """Gets the address_line4 of this InvoiceAddress.  # noqa: E501

        Fourth line of a physical address  # noqa: E501

        :return: The address_line4 of this InvoiceAddress.  # noqa: E501
        :rtype: str
        """
        return self._address_line4

    @address_line4.setter
    def address_line4(self, address_line4):
        """Sets the address_line4 of this InvoiceAddress.

        Fourth line of a physical address  # noqa: E501

        :param address_line4: The address_line4 of this InvoiceAddress.  # noqa: E501
        :type: str
        """

        self._address_line4 = address_line4

    @property
    def city(self):
        """Gets the city of this InvoiceAddress.  # noqa: E501

        City of a physical address  # noqa: E501

        :return: The city of this InvoiceAddress.  # noqa: E501
        :rtype: str
        """
        return self._city

    @city.setter
    def city(self, city):
        """Sets the city of this InvoiceAddress.

        City of a physical address  # noqa: E501

        :param city: The city of this InvoiceAddress.  # noqa: E501
        :type: str
        """

        self._city = city

    @property
    def region(self):
        """Gets the region of this InvoiceAddress.  # noqa: E501

        Region or state of a physical address  # noqa: E501

        :return: The region of this InvoiceAddress.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this InvoiceAddress.

        Region or state of a physical address  # noqa: E501

        :param region: The region of this InvoiceAddress.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def postal_code(self):
        """Gets the postal_code of this InvoiceAddress.  # noqa: E501

        Postal code of a physical address  # noqa: E501

        :return: The postal_code of this InvoiceAddress.  # noqa: E501
        :rtype: str
        """
        return self._postal_code

    @postal_code.setter
    def postal_code(self, postal_code):
        """Sets the postal_code of this InvoiceAddress.

        Postal code of a physical address  # noqa: E501

        :param postal_code: The postal_code of this InvoiceAddress.  # noqa: E501
        :type: str
        """

        self._postal_code = postal_code

    @property
    def country(self):
        """Gets the country of this InvoiceAddress.  # noqa: E501

        Country of a physical address  # noqa: E501

        :return: The country of this InvoiceAddress.  # noqa: E501
        :rtype: str
        """
        return self._country

    @country.setter
    def country(self, country):
        """Sets the country of this InvoiceAddress.

        Country of a physical address  # noqa: E501

        :param country: The country of this InvoiceAddress.  # noqa: E501
        :type: str
        """

        self._country = country
