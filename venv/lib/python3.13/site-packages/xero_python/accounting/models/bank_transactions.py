# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class BankTransactions(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "pagination": "Pagination",
        "warnings": "list[ValidationError]",
        "bank_transactions": "list[BankTransaction]",
    }

    attribute_map = {
        "pagination": "pagination",
        "warnings": "Warnings",
        "bank_transactions": "BankTransactions",
    }

    def __init__(
        self, pagination=None, warnings=None, bank_transactions=None
    ):  # noqa: E501
        """BankTransactions - a model defined in OpenAPI"""  # noqa: E501

        self._pagination = None
        self._warnings = None
        self._bank_transactions = None
        self.discriminator = None

        if pagination is not None:
            self.pagination = pagination
        if warnings is not None:
            self.warnings = warnings
        if bank_transactions is not None:
            self.bank_transactions = bank_transactions

    @property
    def pagination(self):
        """Gets the pagination of this BankTransactions.  # noqa: E501


        :return: The pagination of this BankTransactions.  # noqa: E501
        :rtype: Pagination
        """
        return self._pagination

    @pagination.setter
    def pagination(self, pagination):
        """Sets the pagination of this BankTransactions.


        :param pagination: The pagination of this BankTransactions.  # noqa: E501
        :type: Pagination
        """

        self._pagination = pagination

    @property
    def warnings(self):
        """Gets the warnings of this BankTransactions.  # noqa: E501

        Displays array of warning messages from the API  # noqa: E501

        :return: The warnings of this BankTransactions.  # noqa: E501
        :rtype: list[ValidationError]
        """
        return self._warnings

    @warnings.setter
    def warnings(self, warnings):
        """Sets the warnings of this BankTransactions.

        Displays array of warning messages from the API  # noqa: E501

        :param warnings: The warnings of this BankTransactions.  # noqa: E501
        :type: list[ValidationError]
        """

        self._warnings = warnings

    @property
    def bank_transactions(self):
        """Gets the bank_transactions of this BankTransactions.  # noqa: E501


        :return: The bank_transactions of this BankTransactions.  # noqa: E501
        :rtype: list[BankTransaction]
        """
        return self._bank_transactions

    @bank_transactions.setter
    def bank_transactions(self, bank_transactions):
        """Sets the bank_transactions of this BankTransactions.


        :param bank_transactions: The bank_transactions of this BankTransactions.  # noqa: E501
        :type: list[BankTransaction]
        """

        self._bank_transactions = bank_transactions
