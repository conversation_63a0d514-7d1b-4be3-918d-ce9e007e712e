# coding: utf-8

"""
    Xero Accounting API

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class ImportSummaryAccounts(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "total": "int",
        "new": "int",
        "updated": "int",
        "deleted": "int",
        "locked": "int",
        "system": "int",
        "errored": "int",
        "present": "bool",
        "new_or_updated": "int",
    }

    attribute_map = {
        "total": "Total",
        "new": "New",
        "updated": "Updated",
        "deleted": "Deleted",
        "locked": "Locked",
        "system": "System",
        "errored": "Errored",
        "present": "Present",
        "new_or_updated": "NewOrUpdated",
    }

    def __init__(
        self,
        total=None,
        new=None,
        updated=None,
        deleted=None,
        locked=None,
        system=None,
        errored=None,
        present=None,
        new_or_updated=None,
    ):  # noqa: E501
        """ImportSummaryAccounts - a model defined in OpenAPI"""  # noqa: E501

        self._total = None
        self._new = None
        self._updated = None
        self._deleted = None
        self._locked = None
        self._system = None
        self._errored = None
        self._present = None
        self._new_or_updated = None
        self.discriminator = None

        if total is not None:
            self.total = total
        if new is not None:
            self.new = new
        if updated is not None:
            self.updated = updated
        if deleted is not None:
            self.deleted = deleted
        if locked is not None:
            self.locked = locked
        if system is not None:
            self.system = system
        if errored is not None:
            self.errored = errored
        if present is not None:
            self.present = present
        if new_or_updated is not None:
            self.new_or_updated = new_or_updated

    @property
    def total(self):
        """Gets the total of this ImportSummaryAccounts.  # noqa: E501

        The total number of accounts in the org  # noqa: E501

        :return: The total of this ImportSummaryAccounts.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this ImportSummaryAccounts.

        The total number of accounts in the org  # noqa: E501

        :param total: The total of this ImportSummaryAccounts.  # noqa: E501
        :type: int
        """

        self._total = total

    @property
    def new(self):
        """Gets the new of this ImportSummaryAccounts.  # noqa: E501

        The number of new accounts created  # noqa: E501

        :return: The new of this ImportSummaryAccounts.  # noqa: E501
        :rtype: int
        """
        return self._new

    @new.setter
    def new(self, new):
        """Sets the new of this ImportSummaryAccounts.

        The number of new accounts created  # noqa: E501

        :param new: The new of this ImportSummaryAccounts.  # noqa: E501
        :type: int
        """

        self._new = new

    @property
    def updated(self):
        """Gets the updated of this ImportSummaryAccounts.  # noqa: E501

        The number of accounts updated  # noqa: E501

        :return: The updated of this ImportSummaryAccounts.  # noqa: E501
        :rtype: int
        """
        return self._updated

    @updated.setter
    def updated(self, updated):
        """Sets the updated of this ImportSummaryAccounts.

        The number of accounts updated  # noqa: E501

        :param updated: The updated of this ImportSummaryAccounts.  # noqa: E501
        :type: int
        """

        self._updated = updated

    @property
    def deleted(self):
        """Gets the deleted of this ImportSummaryAccounts.  # noqa: E501

        The number of accounts deleted  # noqa: E501

        :return: The deleted of this ImportSummaryAccounts.  # noqa: E501
        :rtype: int
        """
        return self._deleted

    @deleted.setter
    def deleted(self, deleted):
        """Sets the deleted of this ImportSummaryAccounts.

        The number of accounts deleted  # noqa: E501

        :param deleted: The deleted of this ImportSummaryAccounts.  # noqa: E501
        :type: int
        """

        self._deleted = deleted

    @property
    def locked(self):
        """Gets the locked of this ImportSummaryAccounts.  # noqa: E501

        The number of locked accounts  # noqa: E501

        :return: The locked of this ImportSummaryAccounts.  # noqa: E501
        :rtype: int
        """
        return self._locked

    @locked.setter
    def locked(self, locked):
        """Sets the locked of this ImportSummaryAccounts.

        The number of locked accounts  # noqa: E501

        :param locked: The locked of this ImportSummaryAccounts.  # noqa: E501
        :type: int
        """

        self._locked = locked

    @property
    def system(self):
        """Gets the system of this ImportSummaryAccounts.  # noqa: E501

        The number of system accounts  # noqa: E501

        :return: The system of this ImportSummaryAccounts.  # noqa: E501
        :rtype: int
        """
        return self._system

    @system.setter
    def system(self, system):
        """Sets the system of this ImportSummaryAccounts.

        The number of system accounts  # noqa: E501

        :param system: The system of this ImportSummaryAccounts.  # noqa: E501
        :type: int
        """

        self._system = system

    @property
    def errored(self):
        """Gets the errored of this ImportSummaryAccounts.  # noqa: E501

        The number of accounts that had an error  # noqa: E501

        :return: The errored of this ImportSummaryAccounts.  # noqa: E501
        :rtype: int
        """
        return self._errored

    @errored.setter
    def errored(self, errored):
        """Sets the errored of this ImportSummaryAccounts.

        The number of accounts that had an error  # noqa: E501

        :param errored: The errored of this ImportSummaryAccounts.  # noqa: E501
        :type: int
        """

        self._errored = errored

    @property
    def present(self):
        """Gets the present of this ImportSummaryAccounts.  # noqa: E501


        :return: The present of this ImportSummaryAccounts.  # noqa: E501
        :rtype: bool
        """
        return self._present

    @present.setter
    def present(self, present):
        """Sets the present of this ImportSummaryAccounts.


        :param present: The present of this ImportSummaryAccounts.  # noqa: E501
        :type: bool
        """

        self._present = present

    @property
    def new_or_updated(self):
        """Gets the new_or_updated of this ImportSummaryAccounts.  # noqa: E501

        The number of new or updated accounts  # noqa: E501

        :return: The new_or_updated of this ImportSummaryAccounts.  # noqa: E501
        :rtype: int
        """
        return self._new_or_updated

    @new_or_updated.setter
    def new_or_updated(self, new_or_updated):
        """Sets the new_or_updated of this ImportSummaryAccounts.

        The number of new or updated accounts  # noqa: E501

        :param new_or_updated: The new_or_updated of this ImportSummaryAccounts.  # noqa: E501
        :type: int
        """

        self._new_or_updated = new_or_updated
