# coding: utf-8

"""
    Xero Payroll UK

    This is the Xero Payroll API for orgs in the UK region.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class EmployeeTax(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "starter_type": "str",
        "starter_declaration": "str",
        "tax_code": "str",
        "w1_m1": "bool",
        "previous_taxable_pay": "float",
        "previous_tax_paid": "float",
        "student_loan_deduction": "str",
        "has_post_graduate_loans": "bool",
        "is_director": "bool",
        "directorship_start_date": "date",
        "nic_calculation_method": "str",
    }

    attribute_map = {
        "starter_type": "starterType",
        "starter_declaration": "starterDeclaration",
        "tax_code": "taxCode",
        "w1_m1": "w1M1",
        "previous_taxable_pay": "previousTaxablePay",
        "previous_tax_paid": "previousTaxPaid",
        "student_loan_deduction": "studentLoanDeduction",
        "has_post_graduate_loans": "hasPostGraduateLoans",
        "is_director": "isDirector",
        "directorship_start_date": "directorshipStartDate",
        "nic_calculation_method": "nicCalculationMethod",
    }

    def __init__(
        self,
        starter_type=None,
        starter_declaration=None,
        tax_code=None,
        w1_m1=None,
        previous_taxable_pay=None,
        previous_tax_paid=None,
        student_loan_deduction=None,
        has_post_graduate_loans=None,
        is_director=None,
        directorship_start_date=None,
        nic_calculation_method=None,
    ):  # noqa: E501
        """EmployeeTax - a model defined in OpenAPI"""  # noqa: E501

        self._starter_type = None
        self._starter_declaration = None
        self._tax_code = None
        self._w1_m1 = None
        self._previous_taxable_pay = None
        self._previous_tax_paid = None
        self._student_loan_deduction = None
        self._has_post_graduate_loans = None
        self._is_director = None
        self._directorship_start_date = None
        self._nic_calculation_method = None
        self.discriminator = None

        if starter_type is not None:
            self.starter_type = starter_type
        if starter_declaration is not None:
            self.starter_declaration = starter_declaration
        if tax_code is not None:
            self.tax_code = tax_code
        if w1_m1 is not None:
            self.w1_m1 = w1_m1
        if previous_taxable_pay is not None:
            self.previous_taxable_pay = previous_taxable_pay
        if previous_tax_paid is not None:
            self.previous_tax_paid = previous_tax_paid
        if student_loan_deduction is not None:
            self.student_loan_deduction = student_loan_deduction
        if has_post_graduate_loans is not None:
            self.has_post_graduate_loans = has_post_graduate_loans
        if is_director is not None:
            self.is_director = is_director
        if directorship_start_date is not None:
            self.directorship_start_date = directorship_start_date
        if nic_calculation_method is not None:
            self.nic_calculation_method = nic_calculation_method

    @property
    def starter_type(self):
        """Gets the starter_type of this EmployeeTax.  # noqa: E501

        The Starter type.  # noqa: E501

        :return: The starter_type of this EmployeeTax.  # noqa: E501
        :rtype: str
        """
        return self._starter_type

    @starter_type.setter
    def starter_type(self, starter_type):
        """Sets the starter_type of this EmployeeTax.

        The Starter type.  # noqa: E501

        :param starter_type: The starter_type of this EmployeeTax.  # noqa: E501
        :type: str
        """

        self._starter_type = starter_type

    @property
    def starter_declaration(self):
        """Gets the starter_declaration of this EmployeeTax.  # noqa: E501

        Starter declaration.  # noqa: E501

        :return: The starter_declaration of this EmployeeTax.  # noqa: E501
        :rtype: str
        """
        return self._starter_declaration

    @starter_declaration.setter
    def starter_declaration(self, starter_declaration):
        """Sets the starter_declaration of this EmployeeTax.

        Starter declaration.  # noqa: E501

        :param starter_declaration: The starter_declaration of this EmployeeTax.  # noqa: E501
        :type: str
        """

        self._starter_declaration = starter_declaration

    @property
    def tax_code(self):
        """Gets the tax_code of this EmployeeTax.  # noqa: E501

        The Tax code.  # noqa: E501

        :return: The tax_code of this EmployeeTax.  # noqa: E501
        :rtype: str
        """
        return self._tax_code

    @tax_code.setter
    def tax_code(self, tax_code):
        """Sets the tax_code of this EmployeeTax.

        The Tax code.  # noqa: E501

        :param tax_code: The tax_code of this EmployeeTax.  # noqa: E501
        :type: str
        """

        self._tax_code = tax_code

    @property
    def w1_m1(self):
        """Gets the w1_m1 of this EmployeeTax.  # noqa: E501

        Describes whether the tax settings is W1M1  # noqa: E501

        :return: The w1_m1 of this EmployeeTax.  # noqa: E501
        :rtype: bool
        """
        return self._w1_m1

    @w1_m1.setter
    def w1_m1(self, w1_m1):
        """Sets the w1_m1 of this EmployeeTax.

        Describes whether the tax settings is W1M1  # noqa: E501

        :param w1_m1: The w1_m1 of this EmployeeTax.  # noqa: E501
        :type: bool
        """

        self._w1_m1 = w1_m1

    @property
    def previous_taxable_pay(self):
        """Gets the previous_taxable_pay of this EmployeeTax.  # noqa: E501

        The previous taxable pay  # noqa: E501

        :return: The previous_taxable_pay of this EmployeeTax.  # noqa: E501
        :rtype: float
        """
        return self._previous_taxable_pay

    @previous_taxable_pay.setter
    def previous_taxable_pay(self, previous_taxable_pay):
        """Sets the previous_taxable_pay of this EmployeeTax.

        The previous taxable pay  # noqa: E501

        :param previous_taxable_pay: The previous_taxable_pay of this EmployeeTax.  # noqa: E501
        :type: float
        """

        self._previous_taxable_pay = previous_taxable_pay

    @property
    def previous_tax_paid(self):
        """Gets the previous_tax_paid of this EmployeeTax.  # noqa: E501

        The tax amount previously paid  # noqa: E501

        :return: The previous_tax_paid of this EmployeeTax.  # noqa: E501
        :rtype: float
        """
        return self._previous_tax_paid

    @previous_tax_paid.setter
    def previous_tax_paid(self, previous_tax_paid):
        """Sets the previous_tax_paid of this EmployeeTax.

        The tax amount previously paid  # noqa: E501

        :param previous_tax_paid: The previous_tax_paid of this EmployeeTax.  # noqa: E501
        :type: float
        """

        self._previous_tax_paid = previous_tax_paid

    @property
    def student_loan_deduction(self):
        """Gets the student_loan_deduction of this EmployeeTax.  # noqa: E501

        The employee's student loan deduction type  # noqa: E501

        :return: The student_loan_deduction of this EmployeeTax.  # noqa: E501
        :rtype: str
        """
        return self._student_loan_deduction

    @student_loan_deduction.setter
    def student_loan_deduction(self, student_loan_deduction):
        """Sets the student_loan_deduction of this EmployeeTax.

        The employee's student loan deduction type  # noqa: E501

        :param student_loan_deduction: The student_loan_deduction of this EmployeeTax.  # noqa: E501
        :type: str
        """

        self._student_loan_deduction = student_loan_deduction

    @property
    def has_post_graduate_loans(self):
        """Gets the has_post_graduate_loans of this EmployeeTax.  # noqa: E501

        Describes whether the employee has post graduate loans  # noqa: E501

        :return: The has_post_graduate_loans of this EmployeeTax.  # noqa: E501
        :rtype: bool
        """
        return self._has_post_graduate_loans

    @has_post_graduate_loans.setter
    def has_post_graduate_loans(self, has_post_graduate_loans):
        """Sets the has_post_graduate_loans of this EmployeeTax.

        Describes whether the employee has post graduate loans  # noqa: E501

        :param has_post_graduate_loans: The has_post_graduate_loans of this EmployeeTax.  # noqa: E501
        :type: bool
        """

        self._has_post_graduate_loans = has_post_graduate_loans

    @property
    def is_director(self):
        """Gets the is_director of this EmployeeTax.  # noqa: E501

        Describes whether the employee is director  # noqa: E501

        :return: The is_director of this EmployeeTax.  # noqa: E501
        :rtype: bool
        """
        return self._is_director

    @is_director.setter
    def is_director(self, is_director):
        """Sets the is_director of this EmployeeTax.

        Describes whether the employee is director  # noqa: E501

        :param is_director: The is_director of this EmployeeTax.  # noqa: E501
        :type: bool
        """

        self._is_director = is_director

    @property
    def directorship_start_date(self):
        """Gets the directorship_start_date of this EmployeeTax.  # noqa: E501

        The directorship start date  # noqa: E501

        :return: The directorship_start_date of this EmployeeTax.  # noqa: E501
        :rtype: date
        """
        return self._directorship_start_date

    @directorship_start_date.setter
    def directorship_start_date(self, directorship_start_date):
        """Sets the directorship_start_date of this EmployeeTax.

        The directorship start date  # noqa: E501

        :param directorship_start_date: The directorship_start_date of this EmployeeTax.  # noqa: E501
        :type: date
        """

        self._directorship_start_date = directorship_start_date

    @property
    def nic_calculation_method(self):
        """Gets the nic_calculation_method of this EmployeeTax.  # noqa: E501

        NICs calculation method  # noqa: E501

        :return: The nic_calculation_method of this EmployeeTax.  # noqa: E501
        :rtype: str
        """
        return self._nic_calculation_method

    @nic_calculation_method.setter
    def nic_calculation_method(self, nic_calculation_method):
        """Sets the nic_calculation_method of this EmployeeTax.

        NICs calculation method  # noqa: E501

        :param nic_calculation_method: The nic_calculation_method of this EmployeeTax.  # noqa: E501
        :type: str
        """

        self._nic_calculation_method = nic_calculation_method
