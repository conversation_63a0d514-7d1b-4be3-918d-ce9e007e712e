# coding: utf-8

"""
    Xero Payroll UK

    This is the Xero Payroll API for orgs in the UK region.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class Employees(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "pagination": "Pagination",
        "problem": "Problem",
        "employees": "list[Employee]",
    }

    attribute_map = {
        "pagination": "pagination",
        "problem": "problem",
        "employees": "employees",
    }

    def __init__(self, pagination=None, problem=None, employees=None):  # noqa: E501
        """Employees - a model defined in OpenAPI"""  # noqa: E501

        self._pagination = None
        self._problem = None
        self._employees = None
        self.discriminator = None

        if pagination is not None:
            self.pagination = pagination
        if problem is not None:
            self.problem = problem
        if employees is not None:
            self.employees = employees

    @property
    def pagination(self):
        """Gets the pagination of this Employees.  # noqa: E501


        :return: The pagination of this Employees.  # noqa: E501
        :rtype: Pagination
        """
        return self._pagination

    @pagination.setter
    def pagination(self, pagination):
        """Sets the pagination of this Employees.


        :param pagination: The pagination of this Employees.  # noqa: E501
        :type: Pagination
        """

        self._pagination = pagination

    @property
    def problem(self):
        """Gets the problem of this Employees.  # noqa: E501


        :return: The problem of this Employees.  # noqa: E501
        :rtype: Problem
        """
        return self._problem

    @problem.setter
    def problem(self, problem):
        """Sets the problem of this Employees.


        :param problem: The problem of this Employees.  # noqa: E501
        :type: Problem
        """

        self._problem = problem

    @property
    def employees(self):
        """Gets the employees of this Employees.  # noqa: E501


        :return: The employees of this Employees.  # noqa: E501
        :rtype: list[Employee]
        """
        return self._employees

    @employees.setter
    def employees(self, employees):
        """Sets the employees of this Employees.


        :param employees: The employees of this Employees.  # noqa: E501
        :type: list[Employee]
        """

        self._employees = employees
