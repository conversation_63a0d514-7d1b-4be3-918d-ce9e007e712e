# coding: utf-8

"""
    Xero Payroll UK

    This is the Xero Payroll API for orgs in the UK region.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class EarningsRateObject(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "pagination": "Pagination",
        "problem": "Problem",
        "earnings_rate": "EarningsRate",
    }

    attribute_map = {
        "pagination": "pagination",
        "problem": "problem",
        "earnings_rate": "earningsRate",
    }

    def __init__(self, pagination=None, problem=None, earnings_rate=None):  # noqa: E501
        """EarningsRateObject - a model defined in OpenAPI"""  # noqa: E501

        self._pagination = None
        self._problem = None
        self._earnings_rate = None
        self.discriminator = None

        if pagination is not None:
            self.pagination = pagination
        if problem is not None:
            self.problem = problem
        if earnings_rate is not None:
            self.earnings_rate = earnings_rate

    @property
    def pagination(self):
        """Gets the pagination of this EarningsRateObject.  # noqa: E501


        :return: The pagination of this EarningsRateObject.  # noqa: E501
        :rtype: Pagination
        """
        return self._pagination

    @pagination.setter
    def pagination(self, pagination):
        """Sets the pagination of this EarningsRateObject.


        :param pagination: The pagination of this EarningsRateObject.  # noqa: E501
        :type: Pagination
        """

        self._pagination = pagination

    @property
    def problem(self):
        """Gets the problem of this EarningsRateObject.  # noqa: E501


        :return: The problem of this EarningsRateObject.  # noqa: E501
        :rtype: Problem
        """
        return self._problem

    @problem.setter
    def problem(self, problem):
        """Sets the problem of this EarningsRateObject.


        :param problem: The problem of this EarningsRateObject.  # noqa: E501
        :type: Problem
        """

        self._problem = problem

    @property
    def earnings_rate(self):
        """Gets the earnings_rate of this EarningsRateObject.  # noqa: E501


        :return: The earnings_rate of this EarningsRateObject.  # noqa: E501
        :rtype: EarningsRate
        """
        return self._earnings_rate

    @earnings_rate.setter
    def earnings_rate(self, earnings_rate):
        """Sets the earnings_rate of this EarningsRateObject.


        :param earnings_rate: The earnings_rate of this EarningsRateObject.  # noqa: E501
        :type: EarningsRate
        """

        self._earnings_rate = earnings_rate
