# coding: utf-8

"""
    Xero Payroll UK

    This is the Xero Payroll API for orgs in the UK region.  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""


import re  # noqa: F401

from xero_python.models import BaseModel


class Employment(BaseModel):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        "payroll_calendar_id": "str",
        "start_date": "date",
        "employee_number": "str",
        "ni_category": "NICategoryLetter",
        "ni_categories": "list[NICategory]",
    }

    attribute_map = {
        "payroll_calendar_id": "payrollCalendarID",
        "start_date": "startDate",
        "employee_number": "employeeNumber",
        "ni_category": "niCategory",
        "ni_categories": "niCategories",
    }

    def __init__(
        self,
        payroll_calendar_id=None,
        start_date=None,
        employee_number=None,
        ni_category=None,
        ni_categories=None,
    ):  # noqa: E501
        """Employment - a model defined in OpenAPI"""  # noqa: E501

        self._payroll_calendar_id = None
        self._start_date = None
        self._employee_number = None
        self._ni_category = None
        self._ni_categories = None
        self.discriminator = None

        self.payroll_calendar_id = payroll_calendar_id
        self.start_date = start_date
        self.employee_number = employee_number
        if ni_category is not None:
            self.ni_category = ni_category
        self.ni_categories = ni_categories

    @property
    def payroll_calendar_id(self):
        """Gets the payroll_calendar_id of this Employment.  # noqa: E501

        Xero unique identifier for the payroll calendar of the employee  # noqa: E501

        :return: The payroll_calendar_id of this Employment.  # noqa: E501
        :rtype: str
        """
        return self._payroll_calendar_id

    @payroll_calendar_id.setter
    def payroll_calendar_id(self, payroll_calendar_id):
        """Sets the payroll_calendar_id of this Employment.

        Xero unique identifier for the payroll calendar of the employee  # noqa: E501

        :param payroll_calendar_id: The payroll_calendar_id of this Employment.  # noqa: E501
        :type: str
        """
        if payroll_calendar_id is None:
            raise ValueError(
                "Invalid value for `payroll_calendar_id`, must not be `None`"
            )  # noqa: E501

        self._payroll_calendar_id = payroll_calendar_id

    @property
    def start_date(self):
        """Gets the start_date of this Employment.  # noqa: E501

        Start date of the employment (YYYY-MM-DD)  # noqa: E501

        :return: The start_date of this Employment.  # noqa: E501
        :rtype: date
        """
        return self._start_date

    @start_date.setter
    def start_date(self, start_date):
        """Sets the start_date of this Employment.

        Start date of the employment (YYYY-MM-DD)  # noqa: E501

        :param start_date: The start_date of this Employment.  # noqa: E501
        :type: date
        """
        if start_date is None:
            raise ValueError(
                "Invalid value for `start_date`, must not be `None`"
            )  # noqa: E501

        self._start_date = start_date

    @property
    def employee_number(self):
        """Gets the employee_number of this Employment.  # noqa: E501

        The employment number of the employee  # noqa: E501

        :return: The employee_number of this Employment.  # noqa: E501
        :rtype: str
        """
        return self._employee_number

    @employee_number.setter
    def employee_number(self, employee_number):
        """Sets the employee_number of this Employment.

        The employment number of the employee  # noqa: E501

        :param employee_number: The employee_number of this Employment.  # noqa: E501
        :type: str
        """
        if employee_number is None:
            raise ValueError(
                "Invalid value for `employee_number`, must not be `None`"
            )  # noqa: E501

        self._employee_number = employee_number

    @property
    def ni_category(self):
        """Gets the ni_category of this Employment.  # noqa: E501


        :return: The ni_category of this Employment.  # noqa: E501
        :rtype: NICategoryLetter
        """
        return self._ni_category

    @ni_category.setter
    def ni_category(self, ni_category):
        """Sets the ni_category of this Employment.


        :param ni_category: The ni_category of this Employment.  # noqa: E501
        :type: NICategoryLetter
        """

        self._ni_category = ni_category

    @property
    def ni_categories(self):
        """Gets the ni_categories of this Employment.  # noqa: E501

        The employee's NI categories  # noqa: E501

        :return: The ni_categories of this Employment.  # noqa: E501
        :rtype: list[NICategory]
        """
        return self._ni_categories

    @ni_categories.setter
    def ni_categories(self, ni_categories):
        """Sets the ni_categories of this Employment.

        The employee's NI categories  # noqa: E501

        :param ni_categories: The ni_categories of this Employment.  # noqa: E501
        :type: list[NICategory]
        """
        if ni_categories is None:
            raise ValueError(
                "Invalid value for `ni_categories`, must not be `None`"
            )  # noqa: E501

        self._ni_categories = ni_categories
