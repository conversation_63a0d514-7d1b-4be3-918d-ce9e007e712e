# coding: utf-8

import importlib
import re  # noqa: F401

from xero_python import exceptions
from xero_python.api_client import ApiClient, ModelFinder

try:
    from .exception_handler import translate_status_exception
except ImportError:
    translate_status_exception = exceptions.translate_status_exception

"""
    Xero Projects API

    This is the Xero Projects API  # noqa: E501

    Contact: <EMAIL>
    Generated by: https://openapi-generator.tech
"""

"""
    OpenAPI spec version: 9.0.0
"""


class empty:
    """empty object to mark optional parameter not set"""


class ProjectApi(object):
    """NOTE: This class is auto generated by OpenAPI Generator
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    base_url = "https://api.xero.com/projects.xro/2.0"
    models_module = importlib.import_module("xero_python.project.models")

    def __init__(self, api_client=None, base_url=None):
        if api_client is None:
            api_client = ApiClient()
        self.api_client = api_client
        self.base_url = base_url or self.base_url

    def get_resource_url(self, resource_path):
        """
        Combine API base url with resource specific path
        :param str resource_path: API endpoint specific path
        :return: str full resource path
        """
        return self.base_url + resource_path

    def get_model_finder(self):
        return ModelFinder(self.models_module)

    def create_project(
        self,
        xero_tenant_id,
        project_create_or_update,
        idempotency_key=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Create one or more new projects  # noqa: E501
        OAuth2 scope: projects
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param ProjectCreateOrUpdate project_create_or_update: Create a new project with ProjectCreateOrUpdate object (required)
        :param str idempotency_key: This allows you to safely retry requests without the risk of duplicate processing. 128 character max.
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: Project
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `create_project`"
            )
        # verify the required parameter 'project_create_or_update' is set
        if project_create_or_update is None:
            raise ValueError(
                "Missing the required parameter `project_create_or_update` "
                "when calling `create_project`"
            )

        collection_formats = {}
        path_params = {}

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        if idempotency_key is not empty:
            header_params["Idempotency-Key"] = idempotency_key

        local_var_files = {}
        form_params = []

        body_params = project_create_or_update
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects")

        try:
            return self.api_client.call_api(
                url,
                "POST",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type="Project",
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "create_project")

    def create_task(
        self,
        xero_tenant_id,
        project_id,
        task_create_or_update,
        idempotency_key=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Allows you to create a task  # noqa: E501
        OAuth2 scope: projects
        Allows you to create a specific task  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can create a task on a specified projectId (required)
        :param TaskCreateOrUpdate task_create_or_update: The task object you are creating (required)
        :param str idempotency_key: This allows you to safely retry requests without the risk of duplicate processing. 128 character max.
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: Task
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `create_task`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `create_task`"
            )
        # verify the required parameter 'task_create_or_update' is set
        if task_create_or_update is None:
            raise ValueError(
                "Missing the required parameter `task_create_or_update` "
                "when calling `create_task`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        if idempotency_key is not empty:
            header_params["Idempotency-Key"] = idempotency_key

        local_var_files = {}
        form_params = []

        body_params = task_create_or_update
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}/Tasks")

        try:
            return self.api_client.call_api(
                url,
                "POST",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type="Task",
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "create_task")

    def create_time_entry(
        self,
        xero_tenant_id,
        project_id,
        time_entry_create_or_update,
        idempotency_key=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Creates a time entry for a specific project  # noqa: E501
        OAuth2 scope: projects
        Allows you to create a specific task  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param TimeEntryCreateOrUpdate time_entry_create_or_update: The time entry object you are creating (required)
        :param str idempotency_key: This allows you to safely retry requests without the risk of duplicate processing. 128 character max.
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: TimeEntry
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `create_time_entry`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `create_time_entry`"
            )
        # verify the required parameter 'time_entry_create_or_update' is set
        if time_entry_create_or_update is None:
            raise ValueError(
                "Missing the required parameter `time_entry_create_or_update` "
                "when calling `create_time_entry`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        if idempotency_key is not empty:
            header_params["Idempotency-Key"] = idempotency_key

        local_var_files = {}
        form_params = []

        body_params = time_entry_create_or_update
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}/Time")

        try:
            return self.api_client.call_api(
                url,
                "POST",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type="TimeEntry",
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "create_time_entry")

    def delete_task(
        self,
        xero_tenant_id,
        project_id,
        task_id,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Allows you to delete a task  # noqa: E501
        OAuth2 scope: projects
        Allows you to delete a specific task  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param str task_id: You can specify an individual task by appending the id to the endpoint (required)
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: None
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `delete_task`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `delete_task`"
            )
        # verify the required parameter 'task_id' is set
        if task_id is None:
            raise ValueError(
                "Missing the required parameter `task_id` " "when calling `delete_task`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
            "taskId": task_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        local_var_files = {}
        form_params = []

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}/Tasks/{taskId}")

        try:
            return self.api_client.call_api(
                url,
                "DELETE",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type=None,
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "delete_task")

    def delete_time_entry(
        self,
        xero_tenant_id,
        project_id,
        time_entry_id,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Deletes a time entry for a specific project  # noqa: E501
        OAuth2 scope: projects
        Allows you to delete a specific time entry  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param str time_entry_id: You can specify an individual task by appending the id to the endpoint (required)
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: None
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `delete_time_entry`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `delete_time_entry`"
            )
        # verify the required parameter 'time_entry_id' is set
        if time_entry_id is None:
            raise ValueError(
                "Missing the required parameter `time_entry_id` "
                "when calling `delete_time_entry`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
            "timeEntryId": time_entry_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        local_var_files = {}
        form_params = []

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}/Time/{timeEntryId}")

        try:
            return self.api_client.call_api(
                url,
                "DELETE",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type=None,
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "delete_time_entry")

    def get_project(
        self,
        xero_tenant_id,
        project_id,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Retrieves a single project  # noqa: E501
        OAuth2 scope: projects, projects.read
        Allows you to retrieve a specific project using the projectId  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: Project
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `get_project`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `get_project`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        local_var_files = {}
        form_params = []

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}")

        try:
            return self.api_client.call_api(
                url,
                "GET",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type="Project",
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "get_project")

    def get_project_users(
        self,
        xero_tenant_id,
        page=empty,
        page_size=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Retrieves a list of all project users  # noqa: E501
        OAuth2 scope: projects, projects.read
        Allows you to retrieve the users on a projects.  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param int page: set to 1 by default. The requested number of the page in paged response - Must be a number greater than 0.
        :param int page_size: Optional, it is set to 50 by default. The number of items to return per page in a paged response - Must be a number between 1 and 500.
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: ProjectUsers
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `get_project_users`"
            )

        if page_size is not empty and page_size > 500:
            raise ValueError(
                "Invalid value for parameter `page_size` "
                "when calling `get_project_users`, must be a value "
                "less than or equal to "
                "`500`"
            )
        if page_size is not empty and page_size < 1:
            raise ValueError(
                "Invalid value for parameter `page_size` "
                "when calling `get_project_users`, must be a value "
                "greater than or equal to "
                "`1`"
            )
        collection_formats = {}
        path_params = {}

        query_params = []

        if page is not empty:
            query_params.append(("page", page))

        if page_size is not empty:
            query_params.append(("pageSize", page_size))

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        local_var_files = {}
        form_params = []

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/ProjectsUsers")

        try:
            return self.api_client.call_api(
                url,
                "GET",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type="ProjectUsers",
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "get_project_users")

    def get_projects(
        self,
        xero_tenant_id,
        project_ids=empty,
        contact_id=empty,
        states=empty,
        page=empty,
        page_size=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Retrieves all projects  # noqa: E501
        OAuth2 scope: projects, projects.read
        Allows you to retrieve, create and update projects.  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param list[str] project_ids: Search for all projects that match a comma separated list of projectIds
        :param str contact_id: Filter for projects for a specific contact
        :param str states: Filter for projects in a particular state (INPROGRESS or CLOSED)
        :param int page: set to 1 by default. The requested number of the page in paged response - Must be a number greater than 0.
        :param int page_size: Optional, it is set to 50 by default. The number of items to return per page in a paged response - Must be a number between 1 and 500.
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: Projects
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `get_projects`"
            )

        if page_size is not empty and page_size > 500:
            raise ValueError(
                "Invalid value for parameter `page_size` "
                "when calling `get_projects`, must be a value "
                "less than or equal to "
                "`500`"
            )
        if page_size is not empty and page_size < 1:
            raise ValueError(
                "Invalid value for parameter `page_size` "
                "when calling `get_projects`, must be a value "
                "greater than or equal to "
                "`1`"
            )
        collection_formats = {
            "projectIds": "multi",
        }
        path_params = {}

        query_params = []

        if project_ids is not empty:
            query_params.append(("projectIds", project_ids))

        if contact_id is not empty:
            query_params.append(("contactID", contact_id))

        if states is not empty:
            query_params.append(("states", states))

        if page is not empty:
            query_params.append(("page", page))

        if page_size is not empty:
            query_params.append(("pageSize", page_size))

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        local_var_files = {}
        form_params = []

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects")

        try:
            return self.api_client.call_api(
                url,
                "GET",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type="Projects",
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "get_projects")

    def get_task(
        self,
        xero_tenant_id,
        project_id,
        task_id,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Retrieves a single project task  # noqa: E501
        OAuth2 scope: projects, projects.read
        Allows you to retrieve a specific project  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param str task_id: You can specify an individual task by appending the taskId to the endpoint, i.e. GET https://.../tasks/{taskID} (required)
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: Task
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `get_task`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` " "when calling `get_task`"
            )
        # verify the required parameter 'task_id' is set
        if task_id is None:
            raise ValueError(
                "Missing the required parameter `task_id` " "when calling `get_task`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
            "taskId": task_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        local_var_files = {}
        form_params = []

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}/Tasks/{taskId}")

        try:
            return self.api_client.call_api(
                url,
                "GET",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type="Task",
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "get_task")

    def get_tasks(
        self,
        xero_tenant_id,
        project_id,
        page=empty,
        page_size=empty,
        task_ids=empty,
        charge_type=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Retrieves all project tasks  # noqa: E501
        OAuth2 scope: projects, projects.read
        Allows you to retrieve a specific project  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param int page: Set to 1 by default. The requested number of the page in paged response - Must be a number greater than 0.
        :param int page_size: Optional, it is set to 50 by default. The number of items to return per page in a paged response - Must be a number between 1 and 500.
        :param str task_ids: Search for all tasks that match a comma separated list of taskIds, i.e. GET https://.../tasks?taskIds={taskID},{taskID}
        :param ChargeType charge_type:
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: Tasks
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `get_tasks`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `get_tasks`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
        }

        query_params = []

        if page is not empty:
            query_params.append(("page", page))

        if page_size is not empty:
            query_params.append(("pageSize", page_size))

        if task_ids is not empty:
            query_params.append(("taskIds", task_ids))

        if charge_type is not empty:
            query_params.append(("chargeType", charge_type))

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        local_var_files = {}
        form_params = []

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}/Tasks")

        try:
            return self.api_client.call_api(
                url,
                "GET",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type="Tasks",
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "get_tasks")

    def get_time_entries(
        self,
        xero_tenant_id,
        project_id,
        user_id=empty,
        task_id=empty,
        invoice_id=empty,
        contact_id=empty,
        page=empty,
        page_size=empty,
        states=empty,
        is_chargeable=empty,
        date_after_utc=empty,
        date_before_utc=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Retrieves all time entries associated with a specific project  # noqa: E501
        OAuth2 scope: projects, projects.read
        Allows you to retrieve the time entries associated with a specific project  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: Identifier of the project, that the task (which the time entry is logged against) belongs to. (required)
        :param str user_id: The xero user identifier of the person who logged time.
        :param str task_id: Identifier of the task that time entry is logged against.
        :param str invoice_id: Finds all time entries for this invoice.
        :param str contact_id: Finds all time entries for this contact identifier.
        :param int page: Set to 1 by default. The requested number of the page in paged response - Must be a number greater than 0.
        :param int page_size: Optional, it is set to 50 by default. The number of items to return per page in a paged response - Must be a number between 1 and 500.
        :param list[str] states: Comma-separated list of states to find. Will find all time entries that are in the status of whatever is specified.
        :param bool is_chargeable: Finds all time entries which relate to tasks with the charge type `TIME` or `FIXED`.
        :param datetime date_after_utc: ISO 8601 UTC date. Finds all time entries on or after this date filtered on the `dateUtc` field.
        :param datetime date_before_utc: ISO 8601 UTC date. Finds all time entries on or before this date filtered on the `dateUtc` field.
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: TimeEntries
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `get_time_entries`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `get_time_entries`"
            )

        collection_formats = {
            "states": "multi",
        }
        path_params = {
            "projectId": project_id,
        }

        query_params = []

        if user_id is not empty:
            query_params.append(("userId", user_id))

        if task_id is not empty:
            query_params.append(("taskId", task_id))

        if invoice_id is not empty:
            query_params.append(("invoiceId", invoice_id))

        if contact_id is not empty:
            query_params.append(("contactId", contact_id))

        if page is not empty:
            query_params.append(("page", page))

        if page_size is not empty:
            query_params.append(("pageSize", page_size))

        if states is not empty:
            query_params.append(("states", states))

        if is_chargeable is not empty:
            query_params.append(("isChargeable", is_chargeable))

        if date_after_utc is not empty:
            query_params.append(("dateAfterUtc", date_after_utc))

        if date_before_utc is not empty:
            query_params.append(("dateBeforeUtc", date_before_utc))

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        local_var_files = {}
        form_params = []

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}/Time")

        try:
            return self.api_client.call_api(
                url,
                "GET",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type="TimeEntries",
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "get_time_entries")

    def get_time_entry(
        self,
        xero_tenant_id,
        project_id,
        time_entry_id,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Retrieves a single time entry for a specific project  # noqa: E501
        OAuth2 scope: projects, projects.read
        Allows you to get a single time entry in a project  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param str time_entry_id: You can specify an individual time entry by appending the id to the endpoint (required)
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: TimeEntry
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `get_time_entry`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `get_time_entry`"
            )
        # verify the required parameter 'time_entry_id' is set
        if time_entry_id is None:
            raise ValueError(
                "Missing the required parameter `time_entry_id` "
                "when calling `get_time_entry`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
            "timeEntryId": time_entry_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        local_var_files = {}
        form_params = []

        body_params = None
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}/Time/{timeEntryId}")

        try:
            return self.api_client.call_api(
                url,
                "GET",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type="TimeEntry",
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "get_time_entry")

    def patch_project(
        self,
        xero_tenant_id,
        project_id,
        project_patch,
        idempotency_key=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """creates a project for the specified contact  # noqa: E501
        OAuth2 scope: projects
        Allows you to update a specific projects.  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param ProjectPatch project_patch: Update the status of an existing Project (required)
        :param str idempotency_key: This allows you to safely retry requests without the risk of duplicate processing. 128 character max.
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: None
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `patch_project`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `patch_project`"
            )
        # verify the required parameter 'project_patch' is set
        if project_patch is None:
            raise ValueError(
                "Missing the required parameter `project_patch` "
                "when calling `patch_project`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        if idempotency_key is not empty:
            header_params["Idempotency-Key"] = idempotency_key

        local_var_files = {}
        form_params = []

        body_params = project_patch
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}")

        try:
            return self.api_client.call_api(
                url,
                "PATCH",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type=None,
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "patch_project")

    def update_project(
        self,
        xero_tenant_id,
        project_id,
        project_create_or_update,
        idempotency_key=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Updates a specific project  # noqa: E501
        OAuth2 scope: projects
        Allows you to update a specific projects.  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param ProjectCreateOrUpdate project_create_or_update: Request of type ProjectCreateOrUpdate (required)
        :param str idempotency_key: This allows you to safely retry requests without the risk of duplicate processing. 128 character max.
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: None
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `update_project`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `update_project`"
            )
        # verify the required parameter 'project_create_or_update' is set
        if project_create_or_update is None:
            raise ValueError(
                "Missing the required parameter `project_create_or_update` "
                "when calling `update_project`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        if idempotency_key is not empty:
            header_params["Idempotency-Key"] = idempotency_key

        local_var_files = {}
        form_params = []

        body_params = project_create_or_update
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}")

        try:
            return self.api_client.call_api(
                url,
                "PUT",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type=None,
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "update_project")

    def update_task(
        self,
        xero_tenant_id,
        project_id,
        task_id,
        task_create_or_update,
        idempotency_key=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Allows you to update a task  # noqa: E501
        OAuth2 scope: projects
        Allows you to update a specific task  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param str task_id: You can specify an individual task by appending the id to the endpoint (required)
        :param TaskCreateOrUpdate task_create_or_update: The task object you are updating (required)
        :param str idempotency_key: This allows you to safely retry requests without the risk of duplicate processing. 128 character max.
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: None
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `update_task`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `update_task`"
            )
        # verify the required parameter 'task_id' is set
        if task_id is None:
            raise ValueError(
                "Missing the required parameter `task_id` " "when calling `update_task`"
            )
        # verify the required parameter 'task_create_or_update' is set
        if task_create_or_update is None:
            raise ValueError(
                "Missing the required parameter `task_create_or_update` "
                "when calling `update_task`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
            "taskId": task_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        if idempotency_key is not empty:
            header_params["Idempotency-Key"] = idempotency_key

        local_var_files = {}
        form_params = []

        body_params = task_create_or_update
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}/Tasks/{taskId}")

        try:
            return self.api_client.call_api(
                url,
                "PUT",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type=None,
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "update_task")

    def update_time_entry(
        self,
        xero_tenant_id,
        project_id,
        time_entry_id,
        time_entry_create_or_update,
        idempotency_key=empty,
        _return_http_data_only=True,
        _preload_content=True,
        _request_timeout=None,
    ):
        """Updates a time entry for a specific project  # noqa: E501
        OAuth2 scope: projects
        Allows you to update time entry in a project  # noqa: E501
        :param str xero_tenant_id: Xero identifier for Tenant (required)
        :param str project_id: You can specify an individual project by appending the projectId to the endpoint (required)
        :param str time_entry_id: You can specify an individual time entry by appending the id to the endpoint (required)
        :param TimeEntryCreateOrUpdate time_entry_create_or_update: The time entry object you are updating (required)
        :param str idempotency_key: This allows you to safely retry requests without the risk of duplicate processing. 128 character max.
        :param bool _return_http_data_only: return received data only
        :param bool _preload_content: load received data in models
        :param bool _request_timeout: maximum wait time for response
        :return: None
        """

        # verify the required parameter 'xero_tenant_id' is set
        if xero_tenant_id is None:
            raise ValueError(
                "Missing the required parameter `xero_tenant_id` "
                "when calling `update_time_entry`"
            )
        # verify the required parameter 'project_id' is set
        if project_id is None:
            raise ValueError(
                "Missing the required parameter `project_id` "
                "when calling `update_time_entry`"
            )
        # verify the required parameter 'time_entry_id' is set
        if time_entry_id is None:
            raise ValueError(
                "Missing the required parameter `time_entry_id` "
                "when calling `update_time_entry`"
            )
        # verify the required parameter 'time_entry_create_or_update' is set
        if time_entry_create_or_update is None:
            raise ValueError(
                "Missing the required parameter `time_entry_create_or_update` "
                "when calling `update_time_entry`"
            )

        collection_formats = {}
        path_params = {
            "projectId": project_id,
            "timeEntryId": time_entry_id,
        }

        query_params = []

        header_params = {
            "Xero-Tenant-Id": xero_tenant_id,
        }

        if idempotency_key is not empty:
            header_params["Idempotency-Key"] = idempotency_key

        local_var_files = {}
        form_params = []

        body_params = time_entry_create_or_update
        # HTTP header `Accept`
        header_params["Accept"] = self.api_client.select_header_accept(
            ["application/json"]
        )

        # HTTP header `Content-Type`
        header_params["Content-Type"] = self.api_client.select_header_content_type(
            ["application/json"]
        )

        # Authentication setting
        auth_settings = ["OAuth2"]
        url = self.get_resource_url("/Projects/{projectId}/Time/{timeEntryId}")

        try:
            return self.api_client.call_api(
                url,
                "PUT",
                path_params,
                query_params,
                header_params,
                body=body_params,
                post_params=form_params,
                files=local_var_files,
                response_type=None,
                response_model_finder=self.get_model_finder(),
                auth_settings=auth_settings,
                _return_http_data_only=_return_http_data_only,
                _preload_content=_preload_content,
                _request_timeout=_request_timeout,
                collection_formats=collection_formats,
            )
        except exceptions.HTTPStatusException as error:
            raise translate_status_exception(error, self, "update_time_entry")
